import { Link } from 'react-router-dom';
import { useState } from 'react';
import { Subscription } from '../types';
import { adminApi } from '../api';

interface SubscriptionListProps {
  subscriptions: Subscription[];
  onDelete: (id: string) => void;
  onUpdate: (subscription: Subscription) => void;
}

export default function SubscriptionList({ subscriptions, onDelete, onUpdate }: SubscriptionListProps) {
  const [refreshingIds, setRefreshingIds] = useState<Set<string>>(new Set());
  const formatTraffic = (gb: number) => {
    if (gb >= 1024) {
      return `${(gb / 1024).toFixed(1)} TB`;
    }
    return `${gb.toFixed(1)} GB`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  // 智能状态判断函数
  const getSubscriptionStatus = (subscription: any) => {
    const now = new Date();
    const expiryDate = subscription.expiryDate ? new Date(subscription.expiryDate) : null;
    const usedTraffic = subscription.usedTraffic || 0;
    const totalTraffic = subscription.totalTraffic || 0;

    // 检查是否过期
    if (expiryDate && expiryDate < now) {
      return {
        status: 'expired',
        text: '已过期',
        color: 'text-red-600 bg-red-100',
        icon: '⏰'
      };
    }

    // 检查流量是否耗尽
    if (totalTraffic > 0 && usedTraffic >= totalTraffic) {
      return {
        status: 'traffic_exhausted',
        text: '流量耗尽',
        color: 'text-red-600 bg-red-100',
        icon: '📊'
      };
    }

    // 检查是否无法获取订阅信息（可能的判断条件）
    // 如果最近刷新失败或者数据异常，可以标记为异常
    if (subscription.status === 'suspended' || subscription.status === 'error') {
      return {
        status: 'error',
        text: '异常',
        color: 'text-orange-600 bg-orange-100',
        icon: '⚠️'
      };
    }

    // 检查流量使用率是否过高（警告状态）
    if (totalTraffic > 0 && (usedTraffic / totalTraffic) > 0.9) {
      return {
        status: 'warning',
        text: '流量不足',
        color: 'text-yellow-600 bg-yellow-100',
        icon: '⚡'
      };
    }

    // 正常状态
    return {
      status: 'normal',
      text: '正常',
      color: 'text-green-600 bg-green-100',
      icon: '✅'
    };
  };

  const handleRefreshSubscription = async (id: string) => {
    setRefreshingIds(prev => new Set(prev).add(id));

    try {
      const updatedSubscription = await adminApi.refreshSubscription(id);
      onUpdate(updatedSubscription);
    } catch (error) {
      alert(error instanceof Error ? error.message : '刷新失败');
    } finally {
      setRefreshingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }
  };

  return (
    <div>
      {subscriptions.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-primary-500 mb-4">暂无订阅</div>
          <Link
            to="/admin/subscriptions/new"
            className="btn btn-primary"
          >
            添加第一个订阅
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-sm border border-primary-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-primary-200">
              <thead className="bg-primary-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-primary-500 uppercase tracking-wider">
                    订阅名称
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-primary-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-primary-500 uppercase tracking-wider">
                    流量使用
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-primary-500 uppercase tracking-wider">
                    到期时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-primary-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-primary-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-primary-200">
                {subscriptions.map((subscription) => {
                  const statusInfo = getSubscriptionStatus(subscription);
                  return (
                    <tr key={subscription.id} className="hover:bg-primary-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-primary-900">
                          {subscription.name}
                        </div>
                        <div className="text-sm text-primary-500 truncate max-w-xs">
                          {subscription.url}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full ${statusInfo.color}`}
                        >
                          <span className="mr-1">{statusInfo.icon}</span>
                          {statusInfo.text}
                        </span>
                      </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-primary-900">
                        已用：{formatTraffic(subscription.usedTraffic || 0)} / {formatTraffic(subscription.totalTraffic)}
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div
                          className={`h-2 rounded-full transition-colors ${
                            statusInfo.status === 'traffic_exhausted' || statusInfo.status === 'expired'
                              ? 'bg-red-500'
                              : statusInfo.status === 'warning'
                              ? 'bg-yellow-500'
                              : statusInfo.status === 'error'
                              ? 'bg-orange-500'
                              : 'bg-green-500'
                          }`}
                          style={{
                            width: `${Math.min(100, ((subscription.usedTraffic || 0) / subscription.totalTraffic) * 100)}%`,
                          }}
                        ></div>
                      </div>
                      {/* 显示已用流量百分比 */}
                      <div className="text-xs text-gray-500 mt-1">
                        已用 {subscription.totalTraffic > 0
                          ? `${(((subscription.usedTraffic || 0) / subscription.totalTraffic) * 100).toFixed(1)}%`
                          : '0%'
                        }
                      </div>
                    </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-primary-900">
                        {subscription.expiryDate ? formatDate(subscription.expiryDate) : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-primary-900">
                        {subscription.createdAt ? formatDate(subscription.createdAt) : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => handleRefreshSubscription(subscription.id)}
                            disabled={refreshingIds.has(subscription.id)}
                            className="text-blue-600 hover:text-blue-900 disabled:opacity-50 disabled:cursor-not-allowed"
                            title="刷新订阅信息"
                          >
                            {refreshingIds.has(subscription.id) ? '刷新中...' : '🔄'}
                          </button>
                          <Link
                            to={`/admin/subscriptions/edit/${subscription.id}`}
                            className="text-primary-600 hover:text-primary-900"
                          >
                            编辑
                          </Link>
                          <button
                            onClick={() => onDelete(subscription.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            删除
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
