import { NavLink } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

interface SidebarItem {
  path: string;
  label: string;
  icon: string;
  description?: string;
}

const sidebarItems: SidebarItem[] = [
  {
    path: '/admin',
    label: '总览',
    icon: '📊',
    description: '系统概况和统计'
  },
  {
    path: '/admin/subscriptions',
    label: '管理订阅',
    icon: '📋',
    description: '订阅管理和配置'
  },
  {
    path: '/admin/analytics',
    label: '数据记录',
    icon: '📈',
    description: '流量分析和图表'
  },
  {
    path: '/admin/nodepool',
    label: '节点池',
    icon: '🌐',
    description: '节点聚合和订阅转换'
  },
  {
    path: '/admin/airports',
    label: '机场地址',
    icon: '✈️',
    description: '机场信息和官网'
  },
  {
    path: '/admin/settings',
    label: '系统设置',
    icon: '⚙️',
    description: '系统配置和管理'
  }
];

export default function AdminSidebar() {
  const { logout } = useAuth();

  return (
    <div className="w-60 bg-gray-50 h-screen flex flex-col relative border-r border-gray-200 shadow-sm">
      {/* 头部 */}
      <div className="px-5 py-6 border-b border-gray-200 flex-shrink-0">
        <div>
          <h2 className="text-lg font-medium text-gray-800">管理后台</h2>
          <p className="text-xs text-gray-500 mt-1">机场订阅管理系统</p>
        </div>
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 px-3 py-4 overflow-y-auto pb-32">
        <div className="space-y-1.5">
          {sidebarItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              end={item.path === '/admin'}
              className={({ isActive }) =>
                `flex items-center px-3 py-2.5 rounded-md transition-colors duration-150 ${
                  isActive
                    ? 'bg-white text-gray-800 shadow-sm border border-gray-200'
                    : 'text-gray-600 hover:bg-white hover:text-gray-800'
                }`
              }
            >
              {({ isActive }) => (
                <>
                  <span className="text-lg mr-3 opacity-80">{item.icon}</span>
                  <div className="flex-1">
                    <p className={`text-sm ${isActive ? 'font-medium' : ''}`}>{item.label}</p>
                    {item.description && (
                      <p className={`text-xs ${isActive ? 'text-gray-500' : 'text-gray-400'}`}>
                        {item.description}
                      </p>
                    )}
                  </div>
                </>
              )}
            </NavLink>
          ))}
        </div>
      </nav>

      {/* 底部固定区域 */}
      <div className="absolute bottom-0 left-0 right-0 bg-gray-50">
        {/* 退出登录按钮 */}
        <div className="px-3 py-3 border-t border-gray-200">
          <button
            onClick={logout}
            className="flex w-full items-center px-3 py-2.5 text-gray-600 hover:text-gray-800 hover:bg-white rounded-md transition-colors duration-150 text-sm border border-gray-200 hover:border-gray-300 bg-gray-50"
          >
            <span className="text-base mr-3 opacity-80">🚪</span>
            <span className="font-medium">退出登录</span>
          </button>
        </div>

        {/* 底部信息 */}
        <div className="px-4 py-2 border-t border-gray-200 flex-shrink-0 bg-gray-50">
          <div className="text-center">
            <p className="text-xs text-gray-400">v1.0.0</p>
          </div>
        </div>
      </div>
    </div>
  );
}
