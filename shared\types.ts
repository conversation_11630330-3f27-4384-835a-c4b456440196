export interface Subscription {
  id: string;
  name: string;
  url: string;
  totalTraffic: number; // 总流量 (GB)
  usedTraffic: number;  // 已用流量 (GB)
  remainingTraffic: number; // 剩余流量 (GB)
  expiryDate: string;   // 到期时间
  status: 'active' | 'expired' | 'suspended' | 'error';
  createdAt: string;
  updatedAt: string;
}

export interface CreateSubscriptionRequest {
  name: string;
  url: string;
  totalTraffic: number;
  usedTraffic?: number;
  remainingTraffic?: number;
  status?: 'active' | 'expired' | 'suspended' | 'error';
  expiryDate: string;
}

export interface UpdateSubscriptionRequest {
  name?: string;
  url?: string;
  totalTraffic?: number;
  usedTraffic?: number;
  expiryDate?: string;
  status?: 'active' | 'expired' | 'suspended' | 'error';
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}
