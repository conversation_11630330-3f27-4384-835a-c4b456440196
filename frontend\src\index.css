@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }
  body {
    @apply bg-gray-100 text-gray-800;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-150;
  }
  
  .btn-primary {
    @apply bg-gray-800 text-white hover:bg-gray-700;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
  }
  
  .btn-danger {
    @apply bg-red-500 text-white hover:bg-red-600;
  }
  
  .card {
    @apply bg-white rounded-md shadow-sm border border-gray-200 p-5;
  }
  
  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent;
  }
  
  .admin-card {
    @apply bg-white rounded-md shadow-sm border border-gray-200 p-5;
  }
  
  .admin-section {
    @apply bg-white rounded-md shadow-sm border border-gray-200 p-5 mb-6;
    min-height: 400px;
  }
  
  .admin-section-title {
    @apply text-lg font-medium text-gray-800 mb-4;
  }
}
