import { Link } from 'react-router-dom';
import { CenteredLayout } from './Layout';

export default function NotFound() {
  return (
    <CenteredLayout>
      <div className="text-center">
        <div className="card">
          <div className="text-primary-400 text-8xl mb-4">404</div>
          <h2 className="text-2xl font-bold text-primary-900 mb-2">
            页面未找到
          </h2>
          <p className="text-primary-600 mb-6">
            抱歉，您访问的页面不存在。
          </p>
          <div className="space-y-3">
            <Link
              to="/"
              className="btn btn-primary w-full"
            >
              返回首页
            </Link>
            <div className="text-sm text-primary-500">
              如需访问管理后台，请直接输入 <code className="bg-primary-100 px-1 rounded">/admin</code>
            </div>
          </div>
        </div>
      </div>
    </CenteredLayout>
  );
}
