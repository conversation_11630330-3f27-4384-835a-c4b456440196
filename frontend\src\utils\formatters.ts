/**
 * 格式化流量大小
 */
export function formatTraffic(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const k = 1024;
  const dm = 2;
  
  if (bytes < k) {
    return `${bytes} B`;
  }
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const size = parseFloat((bytes / Math.pow(k, i)).toFixed(dm));
  
  return `${size} ${units[i]}`;
}

/**
 * 格式化GB数据为易读格式
 */
export function formatGB(gb: number): string {
  if (gb === 0) return '0 GB';
  if (gb < 0.01) return '< 0.01 GB';
  if (gb >= 1024) {
    return `${(gb / 1024).toFixed(2)} TB`;
  }
  return `${gb.toFixed(2)} GB`;
}

/**
 * 格式化日期时间
 */
export function formatDateTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch {
    return '无效日期';
  }
}

/**
 * 格式化相对时间（如：2天前）
 */
export function formatRelativeTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return '刚刚';
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}分钟前`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}小时前`;
    } else if (diffInSeconds < 2592000) {
      return `${Math.floor(diffInSeconds / 86400)}天前`;
    } else {
      return formatDateTime(dateString);
    }
  } catch {
    return '无效日期';
  }
}

/**
 * 格式化到期时间，显示剩余天数
 */
export function formatExpiryTime(dateString: string): {
  formatted: string;
  daysLeft: number;
  isExpired: boolean;
  isExpiringSoon: boolean;
} {
  try {
    const expiryDate = new Date(dateString);
    const now = new Date();
    const diffInMs = expiryDate.getTime() - now.getTime();
    const daysLeft = Math.ceil(diffInMs / (1000 * 60 * 60 * 24));
    
    const isExpired = daysLeft < 0;
    const isExpiringSoon = daysLeft <= 7 && daysLeft > 0;
    
    let formatted: string;
    if (isExpired) {
      formatted = `已过期 ${Math.abs(daysLeft)} 天`;
    } else if (daysLeft === 0) {
      formatted = '今天到期';
    } else if (daysLeft === 1) {
      formatted = '明天到期';
    } else if (daysLeft <= 30) {
      formatted = `${daysLeft} 天后到期`;
    } else {
      formatted = formatDateTime(dateString);
    }
    
    return {
      formatted,
      daysLeft,
      isExpired,
      isExpiringSoon
    };
  } catch {
    return {
      formatted: '无效日期',
      daysLeft: 0,
      isExpired: true,
      isExpiringSoon: false
    };
  }
}

/**
 * 格式化百分比
 */
export function formatPercentage(used: number, total: number): number {
  if (total === 0) return 0;
  return Math.min(Math.round((used / total) * 100), 100);
}

/**
 * 获取流量使用状态颜色
 */
export function getTrafficStatusColor(percentage: number): {
  bg: string;
  text: string;
  border: string;
} {
  if (percentage >= 90) {
    return {
      bg: 'bg-red-100',
      text: 'text-red-800',
      border: 'border-red-200'
    };
  } else if (percentage >= 70) {
    return {
      bg: 'bg-yellow-100',
      text: 'text-yellow-800',
      border: 'border-yellow-200'
    };
  } else {
    return {
      bg: 'bg-green-100',
      text: 'text-green-800',
      border: 'border-green-200'
    };
  }
}

/**
 * 获取订阅状态显示信息
 */
export function getSubscriptionStatus(status: string): {
  label: string;
  color: string;
  bgColor: string;
} {
  switch (status) {
    case 'active':
      return {
        label: '正常',
        color: 'text-green-800',
        bgColor: 'bg-green-100'
      };
    case 'expired':
      return {
        label: '已过期',
        color: 'text-red-800',
        bgColor: 'bg-red-100'
      };
    case 'suspended':
      return {
        label: '已暂停',
        color: 'text-yellow-800',
        bgColor: 'bg-yellow-100'
      };
    case 'error':
      return {
        label: '异常',
        color: 'text-gray-800',
        bgColor: 'bg-gray-100'
      };
    default:
      return {
        label: '未知',
        color: 'text-gray-800',
        bgColor: 'bg-gray-100'
      };
  }
}

/**
 * 隐藏敏感URL信息
 */
export function maskUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    // 隐藏token参数
    if (urlObj.searchParams.has('token')) {
      urlObj.searchParams.set('token', '***');
    }
    return urlObj.toString();
  } catch {
    // 如果URL解析失败，使用简单的替换
    return url.replace(/token=[^&]+/g, 'token=***');
  }
}

/**
 * 生成随机颜色（用于图表等）
 */
export function generateColors(count: number): string[] {
  const colors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#06B6D4', '#F97316', '#84CC16', '#EC4899', '#6366F1'
  ];
  
  if (count <= colors.length) {
    return colors.slice(0, count);
  }
  
  // 如果需要更多颜色，生成随机颜色
  const result = [...colors];
  for (let i = colors.length; i < count; i++) {
    const hue = (i * 137.508) % 360; // 黄金角度分布
    result.push(`hsl(${hue}, 70%, 50%)`);
  }
  
  return result;
} 