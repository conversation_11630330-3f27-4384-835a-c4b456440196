import { useState } from 'react';
import { CenteredLayout } from './Layout';

interface LoginProps {
  onLogin: (password: string) => Promise<boolean>;
}

export default function Login({ onLogin }: LoginProps) {
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const success = await onLogin(password);
      if (!success) {
        setError('密码错误，请重试');
        setPassword('');
      }
    } catch (error) {
      setError('登录失败，请检查网络连接');
      console.error('登录错误:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <CenteredLayout>
      <div className="space-y-8">
        <div>
          <h2 className="text-center text-3xl font-bold text-primary-900">
            管理员登录
          </h2>
          <p className="mt-2 text-center text-sm text-primary-600">
            请输入管理员密码以访问后台
          </p>
        </div>
        
        <div className="card">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="text-red-600 text-sm">{error}</div>
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-primary-700 mb-2">
                管理员密码
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="input"
                placeholder="请输入密码"
                disabled={loading}
              />
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? '验证中...' : '登录'}
              </button>
            </div>
          </form>
          
          <div className="mt-6 p-4 bg-primary-50 rounded-lg">
            <div className="text-sm text-primary-600">
              <strong>提示：</strong> 请输入管理员密码
            </div>
            <div className="text-xs text-primary-500 mt-1">
              密码可在环境变量中配置
            </div>
          </div>
        </div>
      </div>
    </CenteredLayout>
  );
}
