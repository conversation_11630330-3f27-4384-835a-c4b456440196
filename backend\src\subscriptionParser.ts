import axios from 'axios';

export interface SubscriptionInfo {
  name?: string;
  totalTraffic?: number;
  usedTraffic?: number;
  remainingTraffic?: number;
  expiryDate?: string;
  status?: 'active' | 'expired' | 'suspended' | 'error';
}

export class SubscriptionParser {
  
  /**
   * 解析订阅链接获取真实信息
   */
  async parseSubscription(url: string): Promise<SubscriptionInfo> {
    console.log('开始解析订阅:', url);

    try {
      // 发送请求获取订阅内容
      const response = await axios.get(url, {
        timeout: 8000, // 减少超时时间到8秒
        headers: {
          'User-Agent': 'ClashforWindows/0.20.39',
          'Accept': '*/*',
          'Accept-Encoding': 'gzip, deflate, br',
          'Cache-Control': 'no-cache'
        },
        validateStatus: (status) => status < 500 // 接受所有小于500的状态码
      });

      console.log('响应状态:', response.status);
      console.log('响应头:', JSON.stringify(response.headers, null, 2));

      const subscriptionContent = response.data;

      // 解析订阅信息
      const info: SubscriptionInfo = {};

      // 从响应头中获取信息 (尝试多种可能的头部名称)
      const headers = response.headers;
      const userInfoHeaders = [
        'subscription-userinfo',
        'Subscription-Userinfo',
        'SUBSCRIPTION-USERINFO',
        'userinfo',
        'Userinfo'
      ];

      let userInfo = null;
      for (const headerName of userInfoHeaders) {
        if (headers[headerName]) {
          userInfo = headers[headerName];
          console.log(`找到用户信息头 ${headerName}:`, userInfo);
          break;
        }
      }

      if (userInfo) {
        const parsed = this.parseUserInfo(userInfo);
        Object.assign(info, parsed);
        console.log('解析的用户信息:', parsed);
      } else {
        console.log('未找到用户信息头部');
      }

      // 解析订阅内容获取节点数量和名称
      if (subscriptionContent) {
        const nodeInfo = this.parseSubscriptionContent(subscriptionContent, url);
        Object.assign(info, nodeInfo);
        console.log('解析的节点信息:', nodeInfo);
      }

      // 如果没有获取到名称，从URL中提取
      if (!info.name) {
        info.name = this.extractNameFromUrl(url);
      }

      console.log('最终解析结果:', info);
      return info;

    } catch (error) {
      console.error('解析订阅失败:', error);

      // 返回基本信息，但状态设置为错误
      const fallbackInfo = {
        name: this.extractNameFromUrl(url),
        status: 'error' as const
      };
      console.log('返回回退信息:', fallbackInfo);
      return fallbackInfo;
    }
  }

  /**
   * 解析 subscription-userinfo 头部信息
   */
  private parseUserInfo(userInfo: string): Partial<SubscriptionInfo> {
    const info: Partial<SubscriptionInfo> = {};

    try {
      console.log('解析用户信息字符串:', userInfo);

      // 解析格式: upload=0; download=1234567890; total=107374182400; expire=1640995200
      // 或者: upload=0&download=1234567890&total=107374182400&expire=1640995200
      const separator = userInfo.includes(';') ? ';' : '&';
      const parts = userInfo.split(separator).map(part => part.trim());

      let upload = 0, download = 0;

      for (const part of parts) {
        if (!part.includes('=')) continue;

        const [key, value] = part.split('=').map(s => s.trim());
        const numValue = parseInt(value);

        console.log(`解析字段: ${key} = ${value} (${numValue})`);

        switch (key.toLowerCase()) {
          case 'total':
            if (numValue > 0) {
              info.totalTraffic = this.bytesToGB(numValue);
              console.log(`总流量: ${info.totalTraffic} GB`);
            }
            break;
          case 'download':
            download = numValue;
            console.log(`下载流量: ${this.bytesToGB(download)} GB`);
            break;
          case 'upload':
            upload = numValue;
            console.log(`上传流量: ${this.bytesToGB(upload)} GB`);
            break;
          case 'expire':
            if (numValue > 0) {
              // 处理时间戳（可能是秒或毫秒）
              const timestamp = numValue > 9999999999 ? numValue : numValue * 1000;
              info.expiryDate = new Date(timestamp).toISOString();
              console.log(`到期时间: ${info.expiryDate}`);
            }
            break;
        }
      }

      // 计算已用流量
      if (upload > 0 || download > 0) {
        info.usedTraffic = this.bytesToGB(upload + download);
        console.log(`已用流量: ${info.usedTraffic} GB (上传: ${this.bytesToGB(upload)} + 下载: ${this.bytesToGB(download)})`);
      }

      // 计算剩余流量
      if (info.totalTraffic !== undefined) {
        const used = info.usedTraffic || 0;
        info.remainingTraffic = Math.max(0, info.totalTraffic - used);
        console.log(`剩余流量: ${info.remainingTraffic} GB (总流量: ${info.totalTraffic} GB, 已用: ${used} GB)`);

        // 如果流量用超了，标记状态为暂停
        if (used > info.totalTraffic) {
          console.log('流量已超额，设置状态为暂停');
          info.status = 'suspended';
        }
      }

      // 判断状态
      if (info.expiryDate) {
        const now = new Date();
        const expiry = new Date(info.expiryDate);
        info.status = now > expiry ? 'expired' : 'active';
        console.log(`订阅状态: ${info.status} (当前: ${now.toISOString()}, 到期: ${expiry.toISOString()})`);
      } else {
        info.status = 'active';
      }

    } catch (error) {
      console.error('解析用户信息失败:', error);
    }

    return info;
  }

  /**
   * 解析订阅内容获取节点信息
   */
  private parseSubscriptionContent(content: string, url: string): Partial<SubscriptionInfo> {
    const info: Partial<SubscriptionInfo> = {};

    try {
      console.log('解析订阅内容，内容类型:', typeof content);
      console.log('内容长度:', content?.length || 0);

      let decodedContent = content;

      // 检查是否是base64编码
      if (typeof content === 'string' && this.isBase64(content.trim())) {
        try {
          decodedContent = Buffer.from(content.trim(), 'base64').toString('utf-8');
          console.log('Base64解码成功，解码后长度:', decodedContent.length);
        } catch (e) {
          console.log('Base64解码失败，使用原始内容');
          decodedContent = content;
        }
      }

      // 尝试多种方式提取订阅名称
      const extractedName = this.extractSubscriptionName(decodedContent, url);
      if (extractedName) {
        info.name = extractedName;
        console.log('提取到订阅名称:', extractedName);
      }

      // 统计节点数量
      const nodeCount = this.countNodes(decodedContent);
      if (nodeCount > 0) {
        console.log('统计到节点数量:', nodeCount);
        if (!info.name) {
          info.name = `机场订阅 (${nodeCount}个节点)`;
        } else {
          info.name += ` (${nodeCount}个节点)`;
        }
      }

    } catch (error) {
      console.error('解析订阅内容失败:', error);
    }

    return info;
  }

  /**
   * 提取订阅名称
   */
  private extractSubscriptionName(content: string, url: string): string | null {
    try {
      // 方法1: 从YAML配置中提取
      if (content.includes('proxies:') || content.includes('proxy-groups:')) {
        // 查找注释中的名称
        const commentMatches = [
          /^#\s*(.+)$/m,
          /^#\s*名称[：:]\s*(.+)$/m,
          /^#\s*name[：:]\s*(.+)$/im,
          /^#\s*title[：:]\s*(.+)$/im
        ];

        for (const regex of commentMatches) {
          const match = content.match(regex);
          if (match && match[1].trim()) {
            return match[1].trim();
          }
        }

        // 从proxy-groups中提取
        const groupMatch = content.match(/proxy-groups:\s*\n\s*-\s*name:\s*(.+)/);
        if (groupMatch && groupMatch[1].trim()) {
          return groupMatch[1].trim();
        }
      }

      // 方法2: 从节点名称中提取共同前缀
      const nodeNames = this.extractNodeNames(content);
      if (nodeNames.length > 0) {
        const commonPrefix = this.findCommonPrefix(nodeNames);
        if (commonPrefix && commonPrefix.length > 2) {
          return commonPrefix.replace(/[-_\s]+$/, ''); // 移除末尾的分隔符
        }
      }

      // 方法3: 从URL路径中提取
      try {
        const urlObj = new URL(url);
        const pathParts = urlObj.pathname.split('/').filter(p => p);
        if (pathParts.length > 0) {
          const lastPart = pathParts[pathParts.length - 1];
          if (lastPart && lastPart !== 'subscribe' && lastPart !== 'sub') {
            return decodeURIComponent(lastPart);
          }
        }
      } catch (e) {
        // URL解析失败，忽略
      }

    } catch (error) {
      console.error('提取订阅名称失败:', error);
    }

    return null;
  }

  /**
   * 统计节点数量
   */
  private countNodes(content: string): number {
    try {
      const lines = content.split('\n').filter(line => line.trim());

      // 统计不同协议的节点
      let count = 0;

      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed.startsWith('ss://') ||
            trimmed.startsWith('vmess://') ||
            trimmed.startsWith('trojan://') ||
            trimmed.startsWith('vless://') ||
            trimmed.startsWith('hy2://') ||
            trimmed.startsWith('hysteria://')) {
          count++;
        }
      }

      // 如果是YAML格式，统计proxies数组
      if (content.includes('proxies:')) {
        const proxyMatches = content.match(/^\s*-\s*(name|server):/gm);
        if (proxyMatches) {
          count = Math.max(count, proxyMatches.length);
        }
      }

      return count;
    } catch (error) {
      console.error('统计节点失败:', error);
      return 0;
    }
  }

  /**
   * 提取节点名称
   */
  private extractNodeNames(content: string): string[] {
    const names: string[] = [];

    try {
      const lines = content.split('\n');

      for (const line of lines) {
        const trimmed = line.trim();

        // 从base64编码的节点链接中提取名称
        if (trimmed.startsWith('vmess://') || trimmed.startsWith('vless://')) {
          try {
            const decoded = Buffer.from(trimmed.split('://')[1], 'base64').toString();
            const config = JSON.parse(decoded);
            if (config.ps || config.add) {
              names.push(config.ps || config.add);
            }
          } catch (e) {
            // 解析失败，忽略
          }
        }

        // 从YAML中提取
        const nameMatch = trimmed.match(/^-?\s*name:\s*(.+)$/);
        if (nameMatch) {
          names.push(nameMatch[1].trim().replace(/['"]/g, ''));
        }
      }
    } catch (error) {
      console.error('提取节点名称失败:', error);
    }

    return names;
  }

  /**
   * 找到字符串数组的公共前缀
   */
  private findCommonPrefix(strings: string[]): string {
    if (strings.length === 0) return '';
    if (strings.length === 1) return strings[0];

    let prefix = strings[0];

    for (let i = 1; i < strings.length; i++) {
      while (strings[i].indexOf(prefix) !== 0) {
        prefix = prefix.substring(0, prefix.length - 1);
        if (prefix === '') return '';
      }
    }

    return prefix;
  }

  /**
   * 从URL中提取名称
   */
  private extractNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname;

      console.log('从URL提取名称:', hostname);

      // 移除常见的前缀
      const cleanHostname = hostname.replace(/^(www\.|api\.|sub\.|subscribe\.|link\.|dl\.)/, '');

      // 提取主域名
      const parts = cleanHostname.split('.');
      if (parts.length >= 2) {
        const mainDomain = parts[parts.length - 2];

        // 常见的机场域名映射
        const domainMappings: { [key: string]: string } = {
          'rixcloud': 'RixCloud',
          'nexitally': 'Nexitally',
          'dlercloud': 'DlerCloud',
          'tagss': 'TagSS',
          'ssrcloud': 'SSRCloud',
          'flyint': 'FlyInt',
          'maying': 'Maying',
          'ytoo': 'Ytoo',
          'sockboom': 'SockBoom',
          'justmysocks': 'JustMySocks',
          'dukou': 'DuKou'
        };

        const mappedName = domainMappings[mainDomain.toLowerCase()];
        if (mappedName) {
          return mappedName;
        }

        // 首字母大写
        const capitalizedName = mainDomain.charAt(0).toUpperCase() + mainDomain.slice(1);
        return `${capitalizedName} 机场`;
      }

      return cleanHostname || '未知机场';
    } catch (e) {
      console.error('从URL提取名称失败:', e);
      return '未知机场';
    }
  }

  /**
   * 字节转换为GB
   */
  private bytesToGB(bytes: number): number {
    return Math.round((bytes / (1024 * 1024 * 1024)) * 100) / 100;
  }

  /**
   * 检查是否是base64编码
   */
  private isBase64(str: string): boolean {
    try {
      // 基本格式检查
      if (!str || str.length === 0) return false;

      // Base64字符集检查
      const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
      if (!base64Regex.test(str)) return false;

      // 长度检查（Base64编码后的长度应该是4的倍数）
      if (str.length % 4 !== 0) return false;

      // 尝试解码和重新编码
      const decoded = Buffer.from(str, 'base64').toString('utf-8');
      const reencoded = Buffer.from(decoded, 'utf-8').toString('base64');

      // 检查解码后的内容是否包含可打印字符
      const hasValidContent = /[\x20-\x7E\n\r\t]/.test(decoded);

      return reencoded === str && hasValidContent && decoded.length > 10;
    } catch {
      return false;
    }
  }

  /**
   * 验证订阅链接格式
   */
  static isValidSubscriptionUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }
}
