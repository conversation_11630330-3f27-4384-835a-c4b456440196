{"name": "airport-subscription-backend", "version": "1.0.0", "description": "机场订阅管理后端API", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"@types/jsonwebtoken": "^9.0.9", "axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "uuid": "^9.0.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "jest": "^29.7.0", "ts-jest": "^29.3.4", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}