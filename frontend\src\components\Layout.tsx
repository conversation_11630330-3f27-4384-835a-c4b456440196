import { ReactNode } from 'react';

interface LayoutProps {
  children: ReactNode;
  fullHeight?: boolean;
  centered?: boolean;
}

export default function Layout({ children, fullHeight = false, centered = false }: LayoutProps) {
  const baseClasses = "px-4 sm:px-6 lg:px-8";
  const heightClasses = fullHeight 
    ? "min-h-[calc(100vh-4rem)]" 
    : "py-6";
  const centerClasses = centered 
    ? "flex items-center justify-center" 
    : "";

  return (
    <div className={`${baseClasses} ${heightClasses} ${centerClasses}`}>
      {children}
    </div>
  );
}

// 专门用于全屏居中的布局（登录、404、错误页面）
export function CenteredLayout({ children }: { children: ReactNode }) {
  return (
    <div
      className="flex items-center justify-center px-4 sm:px-6 lg:px-8"
      style={{
        height: 'calc(100vh - 4rem)',
        marginTop: '-2rem'  // 向上调整以更好地居中
      }}
    >
      <div className="w-full max-w-md">
        {children}
      </div>
    </div>
  );
}

// 专门用于内容页面的布局
export function ContentLayout({ children }: { children: ReactNode }) {
  return (
    <Layout>
      <div className="max-w-7xl mx-auto">
        {children}
      </div>
    </Layout>
  );
}

// 专门用于管理员内容页面的布局
export function AdminContentLayout({ children, title, description }: { 
  children: ReactNode;
  title?: string;
  description?: string;
}) {
  return (
    <div className="space-y-6">
      {(title || description) && (
        <div className="mb-6">
          {title && <h1 className="text-2xl font-medium text-gray-800">{title}</h1>}
          {description && <p className="mt-1 text-sm text-gray-500">{description}</p>}
        </div>
      )}
      <div className="space-y-6">
        {children}
      </div>
    </div>
  );
}
