@echo off
chcp 65001 > nul
echo.
echo 🚀 启动机场订阅管理系统...
echo.

:: 检查Node.js是否已安装
node --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

:: 检查依赖是否已安装
if not exist "backend\node_modules" (
    echo 📦 安装生产依赖...
    cd backend
    call npm install --production
    if errorlevel 1 (
        echo ❌ 依赖安装失败！
        pause
        exit /b 1
    )
    cd ..
)

:: 启动应用
echo.
echo 🎉 启动服务器...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 📱 前端界面: http://localhost:3001
echo 🔧 管理后台: http://localhost:3001/admin  
echo ⚙️  API接口: http://localhost:3001/api
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 💡 提示: 按 Ctrl+C 停止服务
echo.

node start.js

echo.
echo 👋 服务已停止
pause 