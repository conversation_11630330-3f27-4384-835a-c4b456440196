import { useState, useEffect } from 'react';
import { adminApi } from '../api';

interface SystemConfig {
  recordInterval: number; // 智能记录时间间隔（分钟）
}

export default function AdminSettings() {
  const [config, setConfig] = useState<SystemConfig>({
    recordInterval: 60 // 默认60分钟
  });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // 密码更改相关状态
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordChanging, setPasswordChanging] = useState(false);

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      setLoading(true);
      setError(null);
      const systemConfig = await adminApi.getSystemConfig();
      setConfig(systemConfig);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  const saveConfig = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      await adminApi.updateSystemConfig(config);
      setSuccess('配置保存成功，新的记录间隔已立即生效');

      // 3秒后清除成功消息
      setTimeout(() => setSuccess(null), 5000);
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存配置失败');
    } finally {
      setSaving(false);
    }
  };

  const manualUpdateInterval = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const result = await adminApi.updateRecordingInterval();
      setSuccess(`智能记录间隔已手动更新为 ${result.currentInterval} 分钟`);

      // 3秒后清除成功消息
      setTimeout(() => setSuccess(null), 5000);
    } catch (err) {
      setError(err instanceof Error ? err.message : '手动更新失败');
    } finally {
      setSaving(false);
    }
  };

  const changePassword = async () => {
    if (newPassword !== confirmPassword) {
      setError('新密码和确认密码不匹配');
      return;
    }

    if (newPassword.length < 6) {
      setError('新密码长度至少6位');
      return;
    }

    try {
      setPasswordChanging(true);
      setError(null);
      setSuccess(null);

      await adminApi.changePassword(currentPassword, newPassword);
      setSuccess('密码修改成功');
      
      // 清空密码输入框
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      
      // 3秒后清除成功消息
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : '密码修改失败');
    } finally {
      setPasswordChanging(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">系统设置</h1>
          <p className="text-gray-500 mt-1">系统配置和管理</p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-900 border-t-transparent"></div>
          <span className="ml-3 text-gray-600">加载中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">系统设置</h1>
        <p className="text-gray-500 mt-1">系统配置和管理</p>
      </div>

      {/* 错误和成功消息 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <span className="text-red-400 mr-3">❌</span>
            <div className="text-red-700 text-sm">{error}</div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex">
            <span className="text-green-400 mr-3">✅</span>
            <div className="text-green-700 text-sm">{success}</div>
          </div>
        </div>
      )}

      {/* 智能记录设置 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-2">智能记录设置</h2>
          <p className="text-sm text-gray-500">配置自动记录流量数据的时间间隔</p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              记录时间间隔（分钟）
            </label>
            <div className="flex items-center space-x-4">
              <input
                type="number"
                min="1"
                max="1440"
                value={config.recordInterval}
                onChange={(e) => setConfig({ ...config, recordInterval: parseInt(e.target.value) || 60 })}
                className="w-32 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <span className="text-sm text-gray-500">分钟</span>
            </div>
            <p className="text-xs text-gray-400 mt-1">
              建议设置为60分钟，过于频繁可能影响系统性能。设置为1分钟可用于测试。
            </p>
          </div>

          <div className="pt-4 flex space-x-3">
            <button
              onClick={saveConfig}
              disabled={saving}
              className="px-4 py-2 bg-gray-900 text-white text-sm rounded-lg hover:bg-gray-800 focus:outline-none transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  <span>保存中...</span>
                </>
              ) : (
                <>
                  <span>💾</span>
                  <span>保存设置</span>
                </>
              )}
            </button>

            <button
              onClick={manualUpdateInterval}
              disabled={saving}
              className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 focus:outline-none transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  <span>更新中...</span>
                </>
              ) : (
                <>
                  <span>🔄</span>
                  <span>立即应用</span>
                </>
              )}
            </button>
          </div>

          <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="text-xs text-blue-700">
              <strong>说明：</strong>
              <ul className="mt-1 space-y-1">
                <li>• 配置保存后会自动应用，通常在1分钟内生效</li>
                <li>• 如需立即生效，可点击"立即应用"按钮</li>
                <li>• 当前间隔：{config.recordInterval} 分钟</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* 密码修改 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-2">更改管理员密码</h2>
          <p className="text-sm text-gray-500">修改后台管理员登录密码</p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              当前密码
            </label>
            <input
              type="password"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入当前密码"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              新密码
            </label>
            <input
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入新密码（至少6位）"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              确认新密码
            </label>
            <input
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请再次输入新密码"
            />
          </div>

          <div className="pt-4">
            <button
              onClick={changePassword}
              disabled={passwordChanging || !currentPassword || !newPassword || !confirmPassword}
              className="px-4 py-2 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 focus:outline-none transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {passwordChanging ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  <span>修改中...</span>
                </>
              ) : (
                <>
                  <span>🔐</span>
                  <span>修改密码</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
