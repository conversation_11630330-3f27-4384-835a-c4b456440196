# 🐧 CentOS系统部署指南

## 📋 系统要求

- **操作系统**: CentOS 7+ / RHEL 7+ / Rocky Linux 8+
- **CPU**: 1核心
- **内存**: 最少256MB (推荐512MB+)
- **存储**: 1GB可用空间
- **网络**: 可访问外网

## 🚀 快速部署 (推荐)

### 方法一：一键安装脚本

```bash
# 1. 上传部署包到服务器
# 2. 解压部署包
unzip 机场订阅管理系统-部署包.zip
cd web

# 3. 运行一键安装脚本 (需要root权限)
chmod +x install.sh
sudo ./install.sh

# 4. 复制应用文件到系统目录
sudo cp -r ./* /opt/reslms/
sudo chown -R reslms:reslms /opt/reslms

# 5. 切换到应用用户并启动
sudo su - reslms
cd /opt/reslms
./start.sh
```

### 方法二：手动部署

```bash
# 1. 安装Node.js
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 2. 创建应用用户
sudo useradd -r -s /bin/bash -d /opt/reslms reslms
sudo mkdir -p /opt/reslms

# 3. 上传并解压应用文件
# 将web文件夹内容复制到 /opt/reslms/

# 4. 设置权限
sudo chown -R reslms:reslms /opt/reslms
sudo chmod +x /opt/reslms/start.sh

# 5. 安装依赖并启动
sudo su - reslms
cd /opt/reslms
cd backend && npm install --production && cd ..
./start.sh
```

## 🔧 系统服务配置

### 配置为系统服务 (开机自启)

```bash
# 1. 复制服务文件
sudo cp reslms.service /etc/systemd/system/

# 2. 重新加载systemd
sudo systemctl daemon-reload

# 3. 启用服务
sudo systemctl enable reslms

# 4. 启动服务
sudo systemctl start reslms

# 5. 查看状态
sudo systemctl status reslms
```

### 服务管理命令

```bash
# 启动服务
sudo systemctl start reslms

# 停止服务
sudo systemctl stop reslms

# 重启服务
sudo systemctl restart reslms

# 查看状态
sudo systemctl status reslms

# 查看日志
sudo journalctl -u reslms -f

# 禁用开机自启
sudo systemctl disable reslms
```

## 🔥 防火墙配置

### CentOS 7+ (firewalld)

```bash
# 开放3001端口
sudo firewall-cmd --permanent --add-port=3001/tcp
sudo firewall-cmd --reload

# 查看开放的端口
sudo firewall-cmd --list-ports
```

### CentOS 6 (iptables)

```bash
# 开放3001端口
sudo iptables -I INPUT -p tcp --dport 3001 -j ACCEPT
sudo service iptables save
```

## 🌐 Nginx反向代理 (可选)

### 安装Nginx

```bash
sudo yum install -y nginx
```

### 配置反向代理

```bash
# 创建配置文件
sudo nano /etc/nginx/conf.d/reslms.conf
```

配置内容：

```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名

    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

启动Nginx：

```bash
sudo systemctl enable nginx
sudo systemctl start nginx
```

## 🛡️ SSL/HTTPS配置 (推荐)

### 使用Let's Encrypt

```bash
# 安装Certbot
sudo yum install -y epel-release
sudo yum install -y certbot python2-certbot-nginx

# 申请证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和日志

### 查看应用日志

```bash
# 服务日志
sudo journalctl -u reslms -f

# 应用日志（如果使用nohup）
tail -f /opt/reslms/app.log
```

### 性能监控

```bash
# 查看进程状态
ps aux | grep node

# 查看端口占用
netstat -tlnp | grep 3001

# 查看系统资源
top
free -h
df -h
```

## 🔧 常见问题解决

### Q: 端口被占用
```bash
# 查看占用端口的进程
sudo netstat -tlnp | grep 3001
sudo lsof -i :3001

# 杀死进程
sudo kill -9 <PID>
```

### Q: 权限问题
```bash
# 确保文件所有者正确
sudo chown -R reslms:reslms /opt/reslms

# 确保执行权限
sudo chmod +x /opt/reslms/start.sh
```

### Q: Node.js版本问题
```bash
# 卸载旧版本
sudo yum remove -y nodejs npm

# 重新安装
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs
```

### Q: 无法访问
```bash
# 检查服务状态
sudo systemctl status reslms

# 检查防火墙
sudo firewall-cmd --list-ports

# 检查SELinux (如果启用)
sudo setsebool -P httpd_can_network_connect 1
```

## 📱 云服务器配置

### 阿里云ECS
1. 进入ECS控制台
2. 点击"安全组" → "配置规则"
3. 添加入方向规则：端口3001，源地址0.0.0.0/0

### 腾讯云CVM
1. 进入CVM控制台
2. 点击"安全组" → "入站规则"
3. 新建规则：TCP，端口3001源地址0.0.0.0/0

### 华为云ECS
1. 进入ECS控制台
2. 点击"安全组" → "入方向规则"
3. 添加规则：TCP，端口3001，源地址0.0.0.0/0

## 🔄 更新部署

```bash
# 1. 停止服务
sudo systemctl stop reslms

# 2. 备份数据
sudo cp -r /opt/reslms /opt/reslms_backup_$(date +%Y%m%d)

# 3. 更新代码
# 上传新的部署包并解压

# 4. 重新启动
sudo systemctl start reslms
```

## 📞 技术支持

如遇到问题，请提供以下信息：
1. CentOS版本：`cat /etc/centos-release`
2. Node.js版本：`node --version`
3. 服务状态：`sudo systemctl status reslms`
4. 错误日志：`sudo journalctl -u reslms --no-pager`

---

**部署成功后访问**: http://your-server-ip:3001
**管理后台**: http://your-server-ip:3001/admin
**默认密码**: admin123 (请立即修改) 