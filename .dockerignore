# 依赖目录
node_modules/
frontend/node_modules/
backend/node_modules/

# 构建输出 - 注释掉dist目录，我们需要它们
# frontend/dist/
# backend/dist/

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境文件
.env
.env.local
.env.*.local

# 缓存目录
.npm
.eslintcache
.vite/

# 编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
Thumbs.db

# Git相关
.git/
.gitignore

# 测试覆盖率
coverage/

# 数据库文件（构建时不需要）
*.db
*.db-shm
*.db-wal

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 文档
README.md
doc/
*.md

# 备份文件
backup/
*.bak

# 临时文件
tmp/
temp/ 