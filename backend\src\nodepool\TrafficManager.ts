export interface TrafficConfig {
  totalTraffic: number;    // 总流量 (GB)
  expiryDate: string;      // 到期时间
  subscriptionDays?: number; // 订阅周期天数
}

export interface TrafficInfo {
  upload: number;          // 上传流量 (字节)
  download: number;        // 下载流量 (字节)
  total: number;          // 总流量 (字节)
  expire: number;         // 过期时间 (Unix时间戳，秒)
  used: number;           // 已用流量 (字节)
  remaining: number;      // 剩余流量 (字节)
  usagePercent: number;   // 使用百分比
  timePercent: number;    // 时间过去百分比
}

export class TrafficManager {
  private readonly GB_TO_BYTES = 1024 * 1024 * 1024;
  private readonly DAY_TO_MS = 24 * 60 * 60 * 1000;
  
  /**
   * 计算流量信息
   */
  calculateTrafficInfo(config: TrafficConfig, debug: boolean = false): TrafficInfo {
    const currentTime = Date.now();
    const expiryTimestamp = new Date(config.expiryDate).getTime();
    const subscriptionDays = config.subscriptionDays || 30;
    
    if (debug) {
      console.log('=== 流量计算开始 ===');
      console.log('当前时间:', new Date(currentTime).toISOString());
      console.log('过期时间:', new Date(expiryTimestamp).toISOString());
      console.log('订阅周期:', subscriptionDays, '天');
      console.log('总流量:', config.totalTraffic, 'GB');
    }

    // 计算总流量字节数
    const totalBytes = config.totalTraffic * this.GB_TO_BYTES;
    
    // 计算订阅开始时间
    const startTime = this.calculateStartTime(expiryTimestamp, subscriptionDays, currentTime);
    
    // 计算时间相关信息
    const timeInfo = this.calculateTimeInfo(startTime, expiryTimestamp, currentTime, debug);
    
    // 基于时间比例计算已用流量
    const usedBytes = this.calculateUsedTraffic(totalBytes, timeInfo.timePercent);
    const remainingBytes = Math.max(0, totalBytes - usedBytes);
    const usagePercent = totalBytes > 0 ? (usedBytes / totalBytes) * 100 : 0;
    
    // 处理过期时间（秒）
    const expireSeconds = Math.floor(expiryTimestamp / 1000);
    
    if (debug) {
      console.log('计算结果:');
      console.log('- 已用流量:', (usedBytes / this.GB_TO_BYTES).toFixed(2), 'GB');
      console.log('- 剩余流量:', (remainingBytes / this.GB_TO_BYTES).toFixed(2), 'GB');
      console.log('- 使用百分比:', usagePercent.toFixed(2), '%');
      console.log('- 时间百分比:', (timeInfo.timePercent * 100).toFixed(2), '%');
      console.log('=== 流量计算结束 ===');
    }
    
    return {
      upload: 0,              // 暂不区分上传下载
      download: usedBytes,    // 将已用流量作为下载流量
      total: totalBytes,
      expire: expireSeconds,
      used: usedBytes,
      remaining: remainingBytes,
      usagePercent,
      timePercent: timeInfo.timePercent
    };
  }

  /**
   * 计算订阅开始时间
   */
  private calculateStartTime(expiryTime: number, subscriptionDays: number, currentTime: number): number {
    // 基于过期时间减去订阅周期计算开始时间
    let startTime = expiryTime - (subscriptionDays * this.DAY_TO_MS);
    
    // 安全检查：确保开始时间不会过早或过晚
    const maxPastDays = 365; // 最多追溯一年
    const minStartTime = currentTime - (maxPastDays * this.DAY_TO_MS);
    
    if (startTime < minStartTime) {
      console.log('警告：计算的开始时间过早，限制为一年前');
      startTime = minStartTime;
    }
    
    if (startTime > currentTime) {
      console.log('警告：计算的开始时间晚于当前时间，使用当前时间');
      startTime = currentTime;
    }
    
    console.log('订阅开始时间:', new Date(startTime).toISOString());
    return startTime;
  }

  /**
   * 计算时间相关信息
   */
  private calculateTimeInfo(startTime: number, expiryTime: number, currentTime: number, debug: boolean = false) {
    const totalDuration = Math.max(this.DAY_TO_MS, expiryTime - startTime);
    const elapsedTime = Math.max(0, currentTime - startTime);
    const timePercent = Math.min(elapsedTime / totalDuration, 1); // 限制最大为1
    
    if (debug) {
      console.log('时间计算:');
      console.log('- 总订阅时长:', (totalDuration / this.DAY_TO_MS).toFixed(2), '天');
      console.log('- 已过时长:', (elapsedTime / this.DAY_TO_MS).toFixed(2), '天');
      console.log('- 时间比例:', (timePercent * 100).toFixed(2), '%');
    }
    
    return {
      totalDuration,
      elapsedTime,
      timePercent
    };
  }

  /**
   * 计算已用流量
   */
  private calculateUsedTraffic(totalBytes: number, timePercent: number): number {
    // 基于时间比例计算已用流量
    const usedBytes = Math.floor(totalBytes * timePercent);
    
    // 确保不超过总流量
    return Math.min(usedBytes, totalBytes);
  }

  /**
   * 检查订阅是否过期
   */
  isExpired(config: TrafficConfig): boolean {
    const currentTime = Date.now();
    const expiryTime = new Date(config.expiryDate).getTime();
    return currentTime >= expiryTime;
  }

  /**
   * 获取过期状态信息
   */
  getExpiryStatus(config: TrafficConfig): {
    isExpired: boolean;
    daysRemaining: number;
    message: string;
  } {
    const currentTime = Date.now();
    const expiryTime = new Date(config.expiryDate).getTime();
    const isExpired = currentTime >= expiryTime;
    
    const timeDiff = expiryTime - currentTime;
    const daysRemaining = Math.ceil(timeDiff / this.DAY_TO_MS);
    
    let message: string;
    if (isExpired) {
      message = '订阅已过期';
    } else if (daysRemaining <= 3) {
      message = `订阅即将过期，剩余 ${daysRemaining} 天`;
    } else if (daysRemaining <= 7) {
      message = `订阅剩余 ${daysRemaining} 天`;
    } else {
      message = `订阅正常，剩余 ${daysRemaining} 天`;
    }
    
    return {
      isExpired,
      daysRemaining,
      message
    };
  }

  /**
   * 格式化流量大小
   */
  formatTrafficSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 生成流量使用报告
   */
  generateTrafficReport(config: TrafficConfig): string {
    const info = this.calculateTrafficInfo(config);
    const status = this.getExpiryStatus(config);
    
    const report = [
      '=== 流量使用报告 ===',
      `总流量: ${this.formatTrafficSize(info.total)}`,
      `已用流量: ${this.formatTrafficSize(info.used)} (${info.usagePercent.toFixed(1)}%)`,
      `剩余流量: ${this.formatTrafficSize(info.remaining)}`,
      `订阅状态: ${status.message}`,
      `时间进度: ${(info.timePercent * 100).toFixed(1)}%`,
      '====================='
    ];
    
    return report.join('\n');
  }
} 