import React, { useState, useCallback } from 'react';
import { ApiResponse } from '../types';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiReturn<T> extends UseApiState<T> {
  execute: (...args: any[]) => Promise<T | null>;
  reset: () => void;
}

/**
 * 通用API调用钩子
 */
export function useApi<T = any>(
  apiFunction: (...args: any[]) => Promise<ApiResponse<T>>,
  options: {
    onSuccess?: (data: T) => void;
    onError?: (error: string) => void;
    immediate?: boolean;
  } = {}
): UseApiReturn<T> {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null
  });

  const execute = useCallback(
    async (...args: any[]): Promise<T | null> => {
      try {
        setState(prev => ({ ...prev, loading: true, error: null }));
        
        const response = await apiFunction(...args);
        
        if (response.success && response.data) {
          setState({
            data: response.data,
            loading: false,
            error: null
          });
          
          if (options.onSuccess) {
            options.onSuccess(response.data);
          }
          
          return response.data;
        } else {
          const errorMessage = response.message || '请求失败';
          setState({
            data: null,
            loading: false,
            error: errorMessage
          });
          
          if (options.onError) {
            options.onError(errorMessage);
          }
          
          return null;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '网络错误';
        setState({
          data: null,
          loading: false,
          error: errorMessage
        });
        
        if (options.onError) {
          options.onError(errorMessage);
        }
        
        console.error('API调用失败:', error);
        return null;
      }
    },
    [apiFunction, options]
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null
    });
  }, []);

  return {
    ...state,
    execute,
    reset
  };
}

/**
 * 用于列表数据的API钩子
 */
export function useApiList<T = any>(
  apiFunction: () => Promise<ApiResponse<T[]>>,
  options: {
    onSuccess?: (data: T[]) => void;
    onError?: (error: string) => void;
    immediate?: boolean;
  } = {}
) {
  const { immediate = true } = options;
  
  const api = useApi(apiFunction, options);
  
  // 自动加载数据
  React.useEffect(() => {
    if (immediate) {
      api.execute();
    }
  }, [immediate]);
  
  const refresh = useCallback(() => {
    return api.execute();
  }, [api.execute]);
  
  return {
    ...api,
    data: api.data || [],
    refresh
  };
}

/**
 * 用于表单提交的API钩子
 */
export function useApiSubmit<TRequest = any, TResponse = any>(
  apiFunction: (data: TRequest) => Promise<ApiResponse<TResponse>>,
  options: {
    onSuccess?: (data: TResponse, requestData: TRequest) => void;
    onError?: (error: string, requestData: TRequest) => void;
  } = {}
) {
  const [submitting, setSubmitting] = useState(false);
  
  const submit = useCallback(
    async (requestData: TRequest): Promise<TResponse | null> => {
      try {
        setSubmitting(true);
        
        const response = await apiFunction(requestData);
        
        if (response.success && response.data) {
          if (options.onSuccess) {
            options.onSuccess(response.data, requestData);
          }
          return response.data;
        } else {
          const errorMessage = response.message || '提交失败';
          if (options.onError) {
            options.onError(errorMessage, requestData);
          }
          throw new Error(errorMessage);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '网络错误';
        if (options.onError) {
          options.onError(errorMessage, requestData);
        }
        console.error('提交失败:', error);
        throw error;
      } finally {
        setSubmitting(false);
      }
    },
    [apiFunction, options]
  );
  
  return {
    submit,
    submitting
  };
}

/**
 * 用于分页数据的API钩子
 */
export function useApiPagination<T = any>(
  apiFunction: (page: number, pageSize: number) => Promise<ApiResponse<{
    items: T[];
    total: number;
    page: number;
    pageSize: number;
  }>>,
  initialPageSize: number = 10
) {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(initialPageSize);
  
  const api = useApi(apiFunction);
  
  const loadPage = useCallback(
    (newPage: number, newPageSize?: number) => {
      const size = newPageSize || pageSize;
      setPage(newPage);
      if (newPageSize) {
        setPageSize(newPageSize);
      }
      return api.execute(newPage, size);
    },
    [api.execute, pageSize]
  );
  
  const nextPage = useCallback(() => {
    const nextPageNum = page + 1;
    return loadPage(nextPageNum);
  }, [page, loadPage]);
  
  const prevPage = useCallback(() => {
    const prevPageNum = Math.max(1, page - 1);
    return loadPage(prevPageNum);
  }, [page, loadPage]);
  
  const refresh = useCallback(() => {
    return loadPage(page);
  }, [page, loadPage]);
  
  // 初始加载
  React.useEffect(() => {
    loadPage(1);
  }, []);
  
  const paginationData = api.data || { items: [], total: 0, page: 1, pageSize };
  
  return {
    ...api,
    items: paginationData.items,
    total: paginationData.total,
    page: paginationData.page,
    pageSize: paginationData.pageSize,
    totalPages: Math.ceil(paginationData.total / paginationData.pageSize),
    hasNextPage: paginationData.page * paginationData.pageSize < paginationData.total,
    hasPrevPage: paginationData.page > 1,
    loadPage,
    nextPage,
    prevPage,
    refresh
  };
}

/**
 * 带去抖动的搜索API钩子
 */
export function useApiSearch<T = any>(
  apiFunction: (query: string) => Promise<ApiResponse<T[]>>,
  debounceMs: number = 300
) {
  const [query, setQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  
  const api = useApi(apiFunction);
  
  // 去抖动搜索查询
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, debounceMs);
    
    return () => clearTimeout(timer);
  }, [query, debounceMs]);
  
  // 执行搜索
  React.useEffect(() => {
    if (debouncedQuery) {
      api.execute(debouncedQuery);
    } else {
      api.reset();
    }
  }, [debouncedQuery]);
  
  return {
    ...api,
    query,
    setQuery,
    results: api.data || []
  };
} 