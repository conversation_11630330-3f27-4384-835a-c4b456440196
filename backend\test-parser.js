// 简单的测试脚本，用于验证订阅解析功能
const axios = require('axios');

async function testParser() {
  try {
    console.log('测试订阅解析功能...');
    
    // 测试解析API
    const response = await axios.post('http://localhost:3001/api/admin/parse-subscription', {
      url: 'https://example.com/subscribe'  // 这里可以替换为真实的订阅链接
    });
    
    console.log('解析结果:', response.data);
    
  } catch (error) {
    console.error('测试失败:', error.response?.data || error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testParser();
}

module.exports = { testParser };
