import { useState, useEffect } from 'react';
import { adminApi } from '../api';

interface Airport {
  id: string;
  name: string;
  domain: string;
  website?: string;
  description?: string;
  subscriptionCount: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export default function AdminAirports() {
  const [airports, setAirports] = useState<Airport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAirport, setEditingAirport] = useState<Airport | null>(null);

  // 表单数据
  const [formData, setFormData] = useState({
    name: '',
    domain: '',
    website: '',
    description: ''
  });

  useEffect(() => {
    loadAirports();
  }, []);

  const loadAirports = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await adminApi.getAirports();
      setAirports(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载机场信息失败');
    } finally {
      setLoading(false);
    }
  };

  const extractAirportsFromSubscriptions = async () => {
    try {
      setLoading(true);
      const result = await adminApi.extractAirportsFromSubscriptions();
      await loadAirports();
      alert(`机场信息提取完成！\n新增：${result.newCount} 个\n更新：${result.updatedCount} 个`);
    } catch (err) {
      alert('提取机场信息失败：' + (err instanceof Error ? err.message : '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (editingAirport) {
        await adminApi.updateAirport(editingAirport.id, formData);
      } else {
        await adminApi.createAirport(formData);
      }
      
      await loadAirports();
      resetForm();
      alert(editingAirport ? '机场信息更新成功' : '机场信息添加成功');
    } catch (err) {
      alert((editingAirport ? '更新' : '添加') + '机场信息失败：' + (err instanceof Error ? err.message : '未知错误'));
    }
  };

  const handleEdit = (airport: Airport) => {
    setEditingAirport(airport);
    setFormData({
      name: airport.name,
      domain: airport.domain,
      website: airport.website || '',
      description: airport.description || ''
    });
    setShowAddForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这个机场信息吗？')) {
      return;
    }

    try {
      await adminApi.deleteAirport(id);
      await loadAirports();
      alert('机场信息删除成功');
    } catch (err) {
      alert('删除机场信息失败：' + (err instanceof Error ? err.message : '未知错误'));
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      domain: '',
      website: '',
      description: ''
    });
    setEditingAirport(null);
    setShowAddForm(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'inactive':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '活跃';
      case 'inactive':
        return '不活跃';
      default:
        return '未知';
    }
  };

  if (loading && airports.length === 0) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">机场地址</h1>
          <p className="text-gray-500 mt-1">管理机场信息和官网地址</p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-900 border-t-transparent"></div>
          <span className="ml-3 text-gray-600">加载中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">机场地址</h1>
          <p className="text-gray-500 mt-1">管理机场信息和官网地址</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={extractAirportsFromSubscriptions}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 focus:outline-none transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                <span>提取中...</span>
              </>
            ) : (
              <>
                <span>🔍</span>
                <span>从订阅提取</span>
              </>
            )}
          </button>
          <button
            onClick={() => setShowAddForm(true)}
            className="px-4 py-2 bg-gray-900 text-white text-sm rounded-lg hover:bg-gray-800 focus:outline-none transition-colors duration-200 flex items-center space-x-2"
          >
            <span>➕</span>
            <span>添加机场</span>
          </button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <span className="text-red-400 mr-3">❌</span>
            <div className="text-red-700 text-sm">{error}</div>
          </div>
        </div>
      )}

      {/* 添加/编辑表单 */}
      {showAddForm && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium text-gray-900">
              {editingAirport ? '编辑机场信息' : '添加机场信息'}
            </h2>
            <button
              onClick={resetForm}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  机场名称 *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="例如：RixCloud"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  域名 *
                </label>
                <input
                  type="text"
                  value={formData.domain}
                  onChange={(e) => setFormData({ ...formData, domain: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="例如：rixcloud.com"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  官网地址
                </label>
                <input
                  type="url"
                  value={formData.website}
                  onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="例如：https://rixcloud.com"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  描述
                </label>
                <input
                  type="text"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="机场描述信息"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={resetForm}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none transition-colors duration-200"
              >
                取消
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 focus:outline-none transition-colors duration-200"
              >
                {editingAirport ? '更新' : '添加'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* 机场列表 */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        {airports.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">✈️</div>
            <div className="text-gray-500 mb-4">暂无机场信息</div>
            <div className="space-y-2">
              <button
                onClick={extractAirportsFromSubscriptions}
                className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 focus:outline-none transition-colors duration-200"
              >
                从现有订阅中提取机场信息
              </button>
              <div className="text-xs text-gray-400">
                或手动添加机场信息
              </div>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    机场信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    域名
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    订阅数量
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {airports.map((airport) => (
                  <tr key={airport.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{airport.name}</div>
                        {airport.description && (
                          <div className="text-sm text-gray-500">{airport.description}</div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{airport.domain}</div>
                      {airport.website && (
                        <a
                          href={airport.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          访问官网 ↗
                        </a>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{airport.subscriptionCount} 个</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(airport.status)}`}>
                        {getStatusText(airport.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => handleEdit(airport)}
                          className="text-blue-600 hover:text-blue-900"
                          title="编辑"
                        >
                          ✏️
                        </button>
                        <button
                          onClick={() => handleDelete(airport.id)}
                          className="text-red-600 hover:text-red-900"
                          title="删除"
                        >
                          🗑️
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
