import { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { adminApi } from '../api';
import { Subscription } from '../types';
import { AdminContentLayout } from './Layout';

interface OverviewStats {
  totalSubscriptions: number;
  activeSubscriptions: number;
  expiredSubscriptions: number;
  totalTraffic: number;
  usedTraffic: number;
  remainingTraffic: number;
}

interface TrafficRecord {
  id: string;
  subscriptionId: string;
  timestamp: string;
  usedTraffic: number;
  remainingTraffic: number;
  totalTraffic: number;
}

interface ChartData {
  time: string;
  totalUsed: number;
  totalRemaining: number;
  totalTraffic: number;
}

export default function AdminOverview() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [trafficRecords, setTrafficRecords] = useState<TrafficRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [subscriptionsData, trafficData] = await Promise.all([
        adminApi.getSubscriptions(),
        adminApi.getTrafficRecords(undefined, 24).catch(() => [])
      ]);
      setSubscriptions(subscriptionsData);
      setTrafficRecords(trafficData);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载失败');
    } finally {
      setLoading(false);
    }
  };

  // 智能状态判断函数
  const getSubscriptionStatus = (subscription: any) => {
    const now = new Date();
    const expiryDate = subscription.expiryDate ? new Date(subscription.expiryDate) : null;
    const usedTraffic = subscription.usedTraffic || 0;
    const totalTraffic = subscription.totalTraffic || 0;

    // 检查是否过期
    if (expiryDate && expiryDate < now) {
      return {
        status: 'expired',
        text: '已过期',
        color: 'bg-red-100 text-red-800',
        icon: '⏰'
      };
    }

    // 检查流量是否耗尽
    if (totalTraffic > 0 && usedTraffic >= totalTraffic) {
      return {
        status: 'traffic_exhausted',
        text: '流量耗尽',
        color: 'bg-red-100 text-red-800',
        icon: '📊'
      };
    }

    // 检查是否无法获取订阅信息
    if (subscription.status === 'suspended' || subscription.status === 'error') {
      return {
        status: 'error',
        text: '异常',
        color: 'bg-orange-100 text-orange-800',
        icon: '⚠️'
      };
    }

    // 检查流量使用率是否过高（警告状态）
    if (totalTraffic > 0 && (usedTraffic / totalTraffic) > 0.9) {
      return {
        status: 'warning',
        text: '流量不足',
        color: 'bg-yellow-100 text-yellow-800',
        icon: '⚡'
      };
    }

    // 正常状态
    return {
      status: 'normal',
      text: '正常',
      color: 'bg-green-100 text-green-800',
      icon: '✅'
    };
  };

  const calculateStats = (): OverviewStats => {
    return subscriptions.reduce(
      (stats, sub) => {
        const statusInfo = getSubscriptionStatus(sub);
        const isNormal = statusInfo.status === 'normal' || statusInfo.status === 'warning';

        return {
          totalSubscriptions: stats.totalSubscriptions + 1,
          activeSubscriptions: stats.activeSubscriptions + (isNormal ? 1 : 0),
          expiredSubscriptions: stats.expiredSubscriptions + (isNormal ? 0 : 1),
          totalTraffic: stats.totalTraffic + sub.totalTraffic,
          usedTraffic: stats.usedTraffic + (sub.usedTraffic || 0),
          remainingTraffic: stats.remainingTraffic + sub.remainingTraffic,
        };
      },
      {
        totalSubscriptions: 0,
        activeSubscriptions: 0,
        expiredSubscriptions: 0,
        totalTraffic: 0,
        usedTraffic: 0,
        remainingTraffic: 0,
      }
    );
  };

  const prepareChartData = (): ChartData[] => {
    if (!trafficRecords.length) return [];

    // 按时间分组并汇总所有订阅的流量
    const timeGroups: { [key: string]: TrafficRecord[] } = {};

    trafficRecords.forEach(record => {
      const date = new Date(record.timestamp);
      const time = date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });

      if (!timeGroups[time]) {
        timeGroups[time] = [];
      }
      timeGroups[time].push(record);
    });

    // 转换为图表数据格式
    const chartData = Object.entries(timeGroups)
      .sort(([a], [b]) => {
        // 按实际时间排序而不是字符串排序
        const timeA = new Date(timeGroups[a][0].timestamp).getTime();
        const timeB = new Date(timeGroups[b][0].timestamp).getTime();
        return timeA - timeB;
      })
      .map(([time, records]) => {
        const totalUsed = records.reduce((sum, record) => sum + record.usedTraffic, 0);
        const totalRemaining = records.reduce((sum, record) => sum + record.remainingTraffic, 0);
        const totalTraffic = records.reduce((sum, record) => sum + record.totalTraffic, 0);

        return {
          time,
          totalUsed,
          totalRemaining,
          totalTraffic
        };
      });

    // 只显示最近的6次数据
    return chartData.slice(-6);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={loadData}
          className="btn btn-primary"
        >
          重试
        </button>
      </div>
    );
  }

  const stats = calculateStats();
  const usagePercentage = stats.totalTraffic > 0 ? (stats.usedTraffic / stats.totalTraffic) * 100 : 0;
  const chartData = prepareChartData();

  return (
    <AdminContentLayout
      title="系统总览"
      description="机场订阅管理系统概况"
    >
      <div className="flex justify-end mb-4 space-x-3">
        <button
          onClick={async () => {
            if (!confirm('智能记录将刷新所有订阅并记录最新流量数据，可能需要几分钟时间。\n\n包含无效链接的订阅会自动跳过，不会影响整体进度。\n\n是否继续？')) {
              return;
            }

            try {
              setLoading(true);
              const result = await adminApi.smartRecordTraffic();
              alert(`智能流量记录完成！\n\n✅ 成功：${result.successCount} 个\n❌ 失败：${result.failureCount} 个\n\n页面数据已自动刷新。`);
              await loadData(); // 重新加载数据
            } catch (error) {
              const errorMsg = error instanceof Error ? error.message : '未知错误';
              if (errorMsg.includes('timeout')) {
                alert('智能记录超时，这可能是因为网络问题或订阅链接响应缓慢。\n\n建议：\n1. 检查网络连接\n2. 删除无效的订阅链接\n3. 稍后重试');
              } else {
                alert('智能记录失败：' + errorMsg);
              }
            } finally {
              setLoading(false);
            }
          }}
          disabled={loading}
          className="px-3 py-2 bg-gray-900 text-white text-sm rounded-md hover:bg-gray-800 focus:outline-none transition-colors duration-150 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
              <span>记录中...</span>
            </>
          ) : (
            <>
              <span>📊</span>
              <span>智能记录</span>
            </>
          )}
        </button>
        <button
          onClick={async () => {
            if (!confirm('批量刷新将更新所有订阅的流量信息，可能需要几分钟时间。\n\n包含无效链接的订阅会自动跳过。\n\n是否继续？')) {
              return;
            }

            try {
              setLoading(true);
              const result = await adminApi.refreshAllSubscriptions();
              alert(`批量刷新完成！\n\n✅ 成功：${result.successCount} 个\n❌ 失败：${result.failureCount} 个\n\n页面数据已自动刷新。`);
              await loadData(); // 重新加载数据
            } catch (error) {
              const errorMsg = error instanceof Error ? error.message : '未知错误';
              if (errorMsg.includes('timeout')) {
                alert('批量刷新超时，这可能是因为网络问题或订阅链接响应缓慢。\n\n建议：\n1. 检查网络连接\n2. 删除无效的订阅链接\n3. 稍后重试');
              } else {
                alert('批量刷新失败：' + errorMsg);
              }
            } finally {
              setLoading(false);
            }
          }}
          disabled={loading}
          className="px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 focus:outline-none transition-colors duration-150 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
              <span>刷新中...</span>
            </>
          ) : (
            <>
              <span>🔄</span>
              <span>刷新所有订阅</span>
            </>
          )}
        </button>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="admin-card">
          <div className="flex items-center">
            <div className="p-3 rounded-md bg-gray-50">
              <span className="text-xl text-gray-600">📊</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">总订阅数</p>
              <p className="text-2xl font-medium text-gray-800">{stats.totalSubscriptions}</p>
            </div>
          </div>
        </div>

        <div className="admin-card">
          <div className="flex items-center">
            <div className="p-3 rounded-md bg-green-50">
              <span className="text-xl text-green-600">✅</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">正常订阅</p>
              <p className="text-2xl font-medium text-gray-800">{stats.activeSubscriptions}</p>
            </div>
          </div>
        </div>

        <div className="admin-card">
          <div className="flex items-center">
            <div className="p-3 rounded-md bg-red-50">
              <span className="text-xl text-red-600">⚠️</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">异常订阅</p>
              <p className="text-2xl font-medium text-gray-800">{stats.expiredSubscriptions}</p>
            </div>
          </div>
        </div>

        <div className="admin-card">
          <div className="flex items-center">
            <div className="p-3 rounded-md bg-gray-50">
              <span className="text-xl text-gray-600">📈</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">流量使用率</p>
              <p className="text-2xl font-medium text-gray-800">{usagePercentage.toFixed(1)}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* 流量统计 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="admin-section flex flex-col">
          <h3 className="admin-section-title flex-shrink-0">流量统计</h3>
          <div className="flex-shrink-0 space-y-3 mb-4">
            <div>
              <div className="flex justify-between text-sm">
                <span>总流量</span>
                <span>{stats.totalTraffic.toFixed(2)} GB</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '100%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm">
                <span>已使用</span>
                <span>{stats.usedTraffic.toFixed(2)} GB</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                <div
                  className="bg-orange-500 h-2 rounded-full"
                  style={{ width: `${usagePercentage}%` }}
                ></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm">
                <span>剩余流量</span>
                <span>{stats.remainingTraffic.toFixed(2)} GB</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                <div
                  className="bg-green-500 h-2 rounded-full"
                  style={{ width: `${100 - usagePercentage}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* 流量变化趋势图 */}
          <div className="flex-1 flex flex-col min-h-0">
            <div className="border-t border-gray-200 pt-4 flex-1 flex flex-col">
              <h4 className="text-sm font-medium text-gray-700 mb-3 flex-shrink-0">总体流量趋势</h4>
              {chartData.length > 0 ? (
                <div className="flex-1 min-h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={chartData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis
                        dataKey="time"
                        tick={{ fontSize: 10 }}
                        axisLine={false}
                        tickLine={false}
                        height={40}
                      />
                      <YAxis
                        tick={{ fontSize: 10 }}
                        axisLine={false}
                        tickLine={false}
                        width={40}
                      />
                      <Tooltip
                        formatter={(value: number, name: string) => [
                          `${value.toFixed(2)} GB`,
                          name === 'totalUsed' ? '已使用' :
                          name === 'totalRemaining' ? '剩余流量' : '总流量'
                        ]}
                        labelFormatter={(label) => `时间: ${label}`}
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #e5e7eb',
                          borderRadius: '6px',
                          fontSize: '12px'
                        }}
                      />
                      <Line
                        type="monotone"
                        dataKey="totalTraffic"
                        stroke="#3b82f6"
                        strokeWidth={2}
                        dot={{ r: 2 }}
                        activeDot={{ r: 3 }}
                        name="总流量"
                      />
                      <Line
                        type="monotone"
                        dataKey="totalUsed"
                        stroke="#f59e0b"
                        strokeWidth={2}
                        dot={{ r: 2 }}
                        activeDot={{ r: 3 }}
                        name="已使用"
                      />
                      <Line
                        type="monotone"
                        dataKey="totalRemaining"
                        stroke="#10b981"
                        strokeWidth={2}
                        dot={{ r: 2 }}
                        activeDot={{ r: 3 }}
                        name="剩余流量"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="flex-1 flex items-center justify-center text-gray-500 text-sm">
                  暂无流量记录数据
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 最近订阅 */}
        <div className="admin-section">
          <h3 className="admin-section-title">最近添加的订阅</h3>
          <div className="space-y-3">
            {subscriptions.length === 0 ? (
              <p className="text-gray-500 text-sm">暂无订阅数据</p>
            ) : (
              subscriptions
                .sort((a, b) => {
                  const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
                  const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
                  return dateB - dateA;
                })
                .slice(0, 5)
                .map((sub) => {
                  const statusInfo = getSubscriptionStatus(sub);
                  return (
                    <div key={sub.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                      <div>
                        <p className="font-medium text-gray-800">{sub.name}</p>
                        <p className="text-xs text-gray-500">
                          添加于 {sub.createdAt ? new Date(sub.createdAt).toLocaleDateString() : '未知时间'}
                        </p>
                      </div>
                      <div className="flex items-center">
                        <span className={`px-2 py-1 text-xs rounded-full ${statusInfo.color} flex items-center`}>
                          <span className="mr-1">{statusInfo.icon}</span>
                          {statusInfo.text}
                        </span>
                      </div>
                    </div>
                  );
                })
            )}
          </div>
        </div>
      </div>
    </AdminContentLayout>
  );
}
