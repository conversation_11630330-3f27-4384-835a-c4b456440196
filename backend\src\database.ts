import sqlite3 from 'sqlite3';
import { Subscription, CreateSubscriptionRequest, UpdateSubscriptionRequest } from '../../shared/types';
import { v4 as uuidv4 } from 'uuid';

export class Database {
  private db: sqlite3.Database;

  constructor(dbPath: string = './subscriptions.db') {
    this.db = new sqlite3.Database(dbPath);
    this.initTables();
  }

  private initTables(): void {
    const createSubscriptionsTableSQL = `
      CREATE TABLE IF NOT EXISTS subscriptions (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        url TEXT NOT NULL,
        totalTraffic REAL NOT NULL,
        usedTraffic REAL DEFAULT 0,
        remainingTraffic REAL NOT NULL,
        expiryDate TEXT NOT NULL,
        status TEXT DEFAULT 'active',
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )
    `;

    const createTrafficRecordsTableSQL = `
      CREATE TABLE IF NOT EXISTS traffic_records (
        id TEXT PRIMARY KEY,
        subscriptionId TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        usedTraffic REAL NOT NULL,
        remainingTraffic REAL NOT NULL,
        totalTraffic REAL NOT NULL,
        FOREIGN KEY (subscriptionId) REFERENCES subscriptions (id) ON DELETE CASCADE
      )
    `;

    const createSystemConfigTableSQL = `
      CREATE TABLE IF NOT EXISTS system_config (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )
    `;

    const createAirportsTableSQL = `
      CREATE TABLE IF NOT EXISTS airports (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        domain TEXT NOT NULL UNIQUE,
        website TEXT,
        description TEXT,
        status TEXT NOT NULL DEFAULT 'active',
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )
    `;

    const createNodePoolTableSQL = `
      CREATE TABLE IF NOT EXISTS nodepool_config (
        id INTEGER PRIMARY KEY,
        nodeLinks TEXT NOT NULL DEFAULT '',
        subscriptionLinks TEXT NOT NULL DEFAULT '',
        subscriptionName TEXT NOT NULL DEFAULT '',
        totalTraffic INTEGER NOT NULL DEFAULT 99,
        expiryDate TEXT NOT NULL DEFAULT '2099-12-31',
        updateInterval INTEGER NOT NULL DEFAULT 6,
        subConverter TEXT NOT NULL DEFAULT 'SUBAPI.cmliussss.net',
        subConfig TEXT NOT NULL DEFAULT 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/config/ACL4SSR_Online_MultiCountry.ini',
        token TEXT NOT NULL DEFAULT 'auto',
        guestToken TEXT NOT NULL DEFAULT '',
        subscriptionDays INTEGER NOT NULL DEFAULT 30,
        updatedAt TEXT NOT NULL
      )
    `;

    this.db.run(createSubscriptionsTableSQL, (err) => {
      if (err) {
        console.error('创建订阅表失败:', err);
      } else {
        console.log('订阅表初始化成功');

        // 创建流量记录表
        this.db.run(createTrafficRecordsTableSQL, (err) => {
          if (err) {
            console.error('创建流量记录表失败:', err);
          } else {
            console.log('流量记录表初始化成功');

            // 创建系统配置表
            this.db.run(createSystemConfigTableSQL, (err) => {
              if (err) {
                console.error('创建系统配置表失败:', err);
              } else {
                console.log('系统配置表初始化成功');

                // 创建机场信息表
                this.db.run(createAirportsTableSQL, (err) => {
                  if (err) {
                    console.error('创建机场信息表失败:', err);
                  } else {
                    console.log('机场信息表初始化成功');

                    // 创建节点池配置表
                    this.db.run(createNodePoolTableSQL, (err) => {
                      if (err) {
                        console.error('创建节点池配置表失败:', err);
                      } else {
                        console.log('节点池配置表初始化成功');
                        this.migrateNodePoolTable();
                        this.initSystemConfig();
                        this.initNodePoolConfig();
                        this.insertSampleData();
                      }
                    });
                  }
                });
              }
            });
          }
        });
      }
    });
  }

  private insertSampleData(): void {
    // 检查是否已有数据
    this.db.get('SELECT COUNT(*) as count FROM subscriptions', (err, row: any) => {
      if (err) {
        console.error('检查数据失败:', err);
        return;
      }

      if (row.count === 0) {
        console.log('插入示例数据...');
        const sampleData = [
          {
            id: '1',
            name: '高速机场A',
            url: 'https://example-a.com/subscribe/abc123',
            totalTraffic: 100.0,
            usedTraffic: 25.5,
            remainingTraffic: 74.5,
            expiryDate: '2024-12-31T23:59:59.000Z',
            status: 'active',
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-15T10:30:00.000Z'
          },
          {
            id: '2',
            name: '稳定机场B',
            url: 'https://example-b.com/api/subscribe/def456',
            totalTraffic: 200.0,
            usedTraffic: 180.2,
            remainingTraffic: 19.8,
            expiryDate: '2024-06-30T23:59:59.000Z',
            status: 'active',
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-15T10:30:00.000Z'
          },
          {
            id: '3',
            name: '经济机场C',
            url: 'https://example-c.com/sub/ghi789',
            totalTraffic: 50.0,
            usedTraffic: 50.0,
            remainingTraffic: 0.0,
            expiryDate: '2024-01-31T23:59:59.000Z',
            status: 'expired',
            createdAt: '2023-12-01T00:00:00.000Z',
            updatedAt: '2024-01-15T10:30:00.000Z'
          },
          {
            id: '4',
            name: '企业机场D',
            url: 'https://example-d.com/subscription/jkl012',
            totalTraffic: 500.0,
            usedTraffic: 120.8,
            remainingTraffic: 379.2,
            expiryDate: '2024-12-31T23:59:59.000Z',
            status: 'active',
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-15T10:30:00.000Z'
          }
        ];

        const insertSQL = `
          INSERT INTO subscriptions
          (id, name, url, totalTraffic, usedTraffic, remainingTraffic, expiryDate, status, createdAt, updatedAt)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        sampleData.forEach((data) => {
          this.db.run(insertSQL, [
            data.id, data.name, data.url, data.totalTraffic, data.usedTraffic,
            data.remainingTraffic, data.expiryDate, data.status, data.createdAt, data.updatedAt
          ], (err) => {
            if (err) {
              console.error('插入示例数据失败:', err);
            }
          });
        });

        console.log('示例数据插入完成');
      }
    });
  }

  async getAllSubscriptions(): Promise<Subscription[]> {
    return new Promise((resolve, reject) => {
      this.db.all('SELECT * FROM subscriptions ORDER BY createdAt DESC', (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows as Subscription[]);
        }
      });
    });
  }

  async getSubscriptionById(id: string): Promise<Subscription | null> {
    return new Promise((resolve, reject) => {
      this.db.get('SELECT * FROM subscriptions WHERE id = ?', [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row as Subscription || null);
        }
      });
    });
  }

  async createSubscription(data: CreateSubscriptionRequest): Promise<Subscription> {
    const id = uuidv4();
    const now = new Date().toISOString();

    // 使用传递的流量信息，如果没有则使用默认值
    const usedTraffic = data.usedTraffic || 0;
    const remainingTraffic = data.remainingTraffic !== undefined
      ? data.remainingTraffic
      : data.totalTraffic - usedTraffic;

    const subscription: Subscription = {
      id,
      name: data.name,
      url: data.url,
      totalTraffic: data.totalTraffic,
      usedTraffic,
      remainingTraffic,
      expiryDate: data.expiryDate,
      status: data.status || 'active',
      createdAt: now,
      updatedAt: now
    };

    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO subscriptions 
        (id, name, url, totalTraffic, usedTraffic, remainingTraffic, expiryDate, status, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      this.db.run(sql, [
        subscription.id,
        subscription.name,
        subscription.url,
        subscription.totalTraffic,
        subscription.usedTraffic,
        subscription.remainingTraffic,
        subscription.expiryDate,
        subscription.status,
        subscription.createdAt,
        subscription.updatedAt
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(subscription);
        }
      });
    });
  }

  async updateSubscription(id: string, data: UpdateSubscriptionRequest): Promise<Subscription | null> {
    const existing = await this.getSubscriptionById(id);
    if (!existing) return null;

    const totalTraffic = data.totalTraffic || existing.totalTraffic;
    const usedTraffic = data.usedTraffic || existing.usedTraffic;
    const remainingTraffic = Math.max(0, totalTraffic - usedTraffic);

    const updated: Subscription = {
      ...existing,
      ...data,
      remainingTraffic,
      updatedAt: new Date().toISOString()
    };

    return new Promise((resolve, reject) => {
      const sql = `
        UPDATE subscriptions 
        SET name = ?, url = ?, totalTraffic = ?, usedTraffic = ?, 
            remainingTraffic = ?, expiryDate = ?, status = ?, updatedAt = ?
        WHERE id = ?
      `;

      this.db.run(sql, [
        updated.name,
        updated.url,
        updated.totalTraffic,
        updated.usedTraffic,
        updated.remainingTraffic,
        updated.expiryDate,
        updated.status,
        updated.updatedAt,
        id
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(updated);
        }
      });
    });
  }

  async deleteSubscription(id: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.db.run('DELETE FROM subscriptions WHERE id = ?', [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      });
    });
  }

  // 流量记录相关方法
  async createTrafficRecord(subscriptionId: string, usedTraffic: number, remainingTraffic: number, totalTraffic: number): Promise<void> {
    const id = uuidv4();
    const timestamp = new Date().toISOString();

    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO traffic_records
        (id, subscriptionId, timestamp, usedTraffic, remainingTraffic, totalTraffic)
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      this.db.run(sql, [id, subscriptionId, timestamp, usedTraffic, remainingTraffic, totalTraffic], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  async getTrafficRecords(subscriptionId?: string, hours?: number): Promise<any[]> {
    return new Promise((resolve, reject) => {
      let sql = 'SELECT * FROM traffic_records';
      const params: any[] = [];

      if (subscriptionId || hours) {
        sql += ' WHERE';
        const conditions: string[] = [];

        if (subscriptionId) {
          conditions.push(' subscriptionId = ?');
          params.push(subscriptionId);
        }

        if (hours) {
          conditions.push(' timestamp > datetime("now", "-' + hours + ' hours")');
        }

        sql += conditions.join(' AND');
      }

      sql += ' ORDER BY timestamp DESC';

      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  async recordCurrentTraffic(): Promise<void> {
    try {
      const subscriptions = await this.getAllSubscriptions();
      // 使用统一的时间戳，确保所有记录在同一时间点
      const unifiedTimestamp = new Date().toISOString();

      for (const subscription of subscriptions) {
        await this.createTrafficRecordWithTimestamp(
          subscription.id,
          subscription.usedTraffic,
          subscription.remainingTraffic,
          subscription.totalTraffic,
          unifiedTimestamp
        );
      }

      console.log(`记录了 ${subscriptions.length} 个订阅的流量数据，时间戳: ${unifiedTimestamp}`);
    } catch (error) {
      console.error('记录流量数据失败:', error);
    }
  }

  // 创建带指定时间戳的流量记录
  async createTrafficRecordWithTimestamp(subscriptionId: string, usedTraffic: number, remainingTraffic: number, totalTraffic: number, timestamp: string): Promise<void> {
    const id = uuidv4();

    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO traffic_records
        (id, subscriptionId, timestamp, usedTraffic, remainingTraffic, totalTraffic)
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      this.db.run(sql, [id, subscriptionId, timestamp, usedTraffic, remainingTraffic, totalTraffic], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  // 智能记录流量：先刷新订阅信息，再记录流量
  async recordTrafficWithRefresh(parser: any): Promise<{ successCount: number; failureCount: number }> {
    try {
      const subscriptions = await this.getAllSubscriptions();
      let successCount = 0;
      let failureCount = 0;

      // 使用统一的时间戳，确保所有记录在同一时间点
      const unifiedTimestamp = new Date().toISOString();
      console.log(`开始智能记录 ${subscriptions.length} 个订阅的流量... 时间戳: ${unifiedTimestamp}`);

      // 设置整体超时时间（最多5分钟）
      const overallTimeout = 5 * 60 * 1000; // 5分钟
      const startTime = Date.now();

      // 批量刷新订阅信息，减少批量大小以提高响应性
      const batchSize = 2; // 减少到2个并发
      for (let i = 0; i < subscriptions.length; i += batchSize) {
        // 检查是否超过整体超时时间
        if (Date.now() - startTime > overallTimeout) {
          console.warn(`智能记录超时，已处理 ${i} 个订阅，剩余 ${subscriptions.length - i} 个跳过`);
          break;
        }

        const batch = subscriptions.slice(i, i + batchSize);

        await Promise.all(batch.map(async (subscription) => {
          // 为每个订阅设置单独的超时
          const subscriptionTimeout = 10000; // 10秒超时

          try {
            // 使用Promise.race实现超时控制
            const parsePromise = parser.parseSubscription(subscription.url);
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('订阅解析超时')), subscriptionTimeout);
            });

            const parsedInfo = await Promise.race([parsePromise, timeoutPromise]);

            // 更新订阅信息
            const updateData = {
              totalTraffic: parsedInfo.totalTraffic || subscription.totalTraffic,
              usedTraffic: parsedInfo.usedTraffic || 0,
              status: parsedInfo.status || 'active',
              ...(parsedInfo.expiryDate && { expiryDate: parsedInfo.expiryDate })
            };

            await this.updateSubscription(subscription.id, updateData);

            // 使用统一时间戳记录最新的流量数据
            await this.createTrafficRecordWithTimestamp(
              subscription.id,
              updateData.usedTraffic,
              (updateData.totalTraffic - updateData.usedTraffic),
              updateData.totalTraffic,
              unifiedTimestamp
            );

            console.log(`✓ 智能记录订阅 ${subscription.name} 成功`);
            successCount++;
          } catch (error) {
            console.error(`✗ 智能记录订阅 ${subscription.name} 失败:`, (error as Error).message || error);

            // 解析失败时，仍然记录当前数据库中的数据
            try {
              await this.updateSubscription(subscription.id, { status: 'error' as const });
              await this.createTrafficRecordWithTimestamp(
                subscription.id,
                subscription.usedTraffic,
                subscription.remainingTraffic,
                subscription.totalTraffic,
                unifiedTimestamp
              );
              console.log(`  → 已记录 ${subscription.name} 的当前数据`);
            } catch (fallbackError) {
              console.error(`  → 备用记录订阅 ${subscription.name} 也失败:`, fallbackError);
            }

            failureCount++;
          }
        }));

        // 批次间短暂延迟，避免过度并发
        if (i + batchSize < subscriptions.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      const duration = ((Date.now() - startTime) / 1000).toFixed(1);
      console.log(`智能流量记录完成：成功 ${successCount} 个，失败 ${failureCount} 个，耗时 ${duration} 秒`);
      return { successCount, failureCount };
    } catch (error) {
      console.error('智能流量记录失败:', error);
      throw error;
    }
  }

  // 清理不完整的流量记录（同一时间点缺少某些订阅的记录）
  async cleanupIncompleteRecords(): Promise<void> {
    try {
      const subscriptions = await this.getAllSubscriptions();
      const expectedCount = subscriptions.length;

      if (expectedCount === 0) return;

      // 获取所有记录按时间分组
      const records = await this.getTrafficRecords();
      const timeGroups: { [key: string]: any[] } = {};

      records.forEach(record => {
        const timeKey = new Date(record.timestamp).toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        });

        if (!timeGroups[timeKey]) {
          timeGroups[timeKey] = [];
        }
        timeGroups[timeKey].push(record);
      });

      // 找出不完整的时间组
      const incompleteTimeGroups = Object.entries(timeGroups)
        .filter(([_, records]) => records.length !== expectedCount)
        .map(([time, records]) => ({ time, records, count: records.length }));

      if (incompleteTimeGroups.length > 0) {
        console.log(`发现 ${incompleteTimeGroups.length} 个不完整的时间组：`);
        incompleteTimeGroups.forEach(group => {
          console.log(`  ${group.time}: ${group.count}/${expectedCount} 条记录`);
        });

        // 删除不完整的记录
        for (const group of incompleteTimeGroups) {
          for (const record of group.records) {
            await new Promise<void>((resolve, reject) => {
              this.db.run('DELETE FROM traffic_records WHERE id = ?', [record.id], (err) => {
                if (err) reject(err);
                else resolve();
              });
            });
          }
        }

        console.log(`已清理 ${incompleteTimeGroups.reduce((sum, g) => sum + g.count, 0)} 条不完整的记录`);
      }
    } catch (error) {
      console.error('清理不完整记录失败:', error);
    }
  }

  // 初始化系统配置
  private initSystemConfig(): void {
    const defaultConfig = [
      { key: 'recordInterval', value: '60' } // 默认60分钟
    ];

    defaultConfig.forEach(config => {
      this.db.get('SELECT value FROM system_config WHERE key = ?', [config.key], (err, row) => {
        if (err) {
          console.error('检查系统配置失败:', err);
          return;
        }

        if (!row) {
          // 配置不存在，插入默认值
          const now = new Date().toISOString();
          this.db.run(
            'INSERT INTO system_config (key, value, updatedAt) VALUES (?, ?, ?)',
            [config.key, config.value, now],
            (err) => {
              if (err) {
                console.error(`插入默认配置 ${config.key} 失败:`, err);
              } else {
                console.log(`初始化系统配置: ${config.key} = ${config.value}`);
              }
            }
          );
        }
      });
    });
  }

  // 获取系统配置
  async getSystemConfig(): Promise<{ recordInterval: number }> {
    return new Promise((resolve, reject) => {
      this.db.get('SELECT value FROM system_config WHERE key = ?', ['recordInterval'], (err, row: any) => {
        if (err) {
          reject(err);
        } else {
          const recordInterval = row ? parseInt(row.value) : 60;
          resolve({ recordInterval });
        }
      });
    });
  }

  // 更新系统配置
  async updateSystemConfig(config: { recordInterval: number }): Promise<void> {
    const now = new Date().toISOString();

    return new Promise((resolve, reject) => {
      this.db.run(
        'UPDATE system_config SET value = ?, updatedAt = ? WHERE key = ?',
        [config.recordInterval.toString(), now, 'recordInterval'],
        function(err) {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }

  // 获取所有机场信息
  async getAllAirports(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT
          a.*,
          COUNT(s.id) as subscriptionCount
        FROM airports a
        LEFT JOIN subscriptions s ON s.url LIKE '%' || a.domain || '%'
        GROUP BY a.id
        ORDER BY a.name
      `;

      this.db.all(sql, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  // 创建机场信息
  async createAirport(data: { name: string; domain: string; website?: string; description?: string }): Promise<any> {
    const id = uuidv4();
    const now = new Date().toISOString();

    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO airports (id, name, domain, website, description, status, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, 'active', ?, ?)
      `;

      this.db.run(sql, [
        id,
        data.name,
        data.domain,
        data.website || null,
        data.description || null,
        now,
        now
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({
            id,
            name: data.name,
            domain: data.domain,
            website: data.website,
            description: data.description,
            status: 'active',
            createdAt: now,
            updatedAt: now,
            subscriptionCount: 0
          });
        }
      });
    });
  }

  // 更新机场信息
  async updateAirport(id: string, data: { name?: string; domain?: string; website?: string; description?: string }): Promise<any> {
    const now = new Date().toISOString();
    const updates: string[] = [];
    const values: any[] = [];

    if (data.name !== undefined) {
      updates.push('name = ?');
      values.push(data.name);
    }
    if (data.domain !== undefined) {
      updates.push('domain = ?');
      values.push(data.domain);
    }
    if (data.website !== undefined) {
      updates.push('website = ?');
      values.push(data.website || null);
    }
    if (data.description !== undefined) {
      updates.push('description = ?');
      values.push(data.description || null);
    }

    updates.push('updatedAt = ?');
    values.push(now);
    values.push(id);

    return new Promise((resolve, reject) => {
      const sql = `UPDATE airports SET ${updates.join(', ')} WHERE id = ?`;

      this.db.run(sql, values, function(err) {
        if (err) {
          reject(err);
        } else if (this.changes === 0) {
          reject(new Error('机场信息不存在'));
        } else {
          // 获取更新后的数据
          resolve({ id, ...data, updatedAt: now });
        }
      });
    });
  }

  // 删除机场信息
  async deleteAirport(id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db.run('DELETE FROM airports WHERE id = ?', [id], function(err) {
        if (err) {
          reject(err);
        } else if (this.changes === 0) {
          reject(new Error('机场信息不存在'));
        } else {
          resolve();
        }
      });
    });
  }

  // 从订阅中提取机场信息
  async extractAirportsFromSubscriptions(): Promise<{ newCount: number; updatedCount: number }> {
    const subscriptions = await this.getAllSubscriptions();
    let newCount = 0;
    let updatedCount = 0;

    for (const subscription of subscriptions) {
      try {
        const url = new URL(subscription.url);
        const domain = url.hostname.replace(/^(www\.|api\.|sub\.|subscribe\.|link\.|dl\.)/, '');

        // 检查机场是否已存在
        const existingAirport = await new Promise<any>((resolve, reject) => {
          this.db.get('SELECT * FROM airports WHERE domain = ?', [domain], (err, row) => {
            if (err) reject(err);
            else resolve(row);
          });
        });

        if (!existingAirport) {
          // 创建新机场信息
          const airportName = this.extractAirportNameFromSubscription(subscription.name, domain);
          await this.createAirport({
            name: airportName,
            domain: domain,
            website: `https://${domain}`,
            description: `从订阅 "${subscription.name}" 中提取`
          });
          newCount++;
        } else {
          // 更新现有机场的状态为活跃
          await new Promise<void>((resolve, reject) => {
            const now = new Date().toISOString();
            this.db.run(
              'UPDATE airports SET status = ?, updatedAt = ? WHERE id = ?',
              ['active', now, existingAirport.id],
              (err) => {
                if (err) reject(err);
                else resolve();
              }
            );
          });
          updatedCount++;
        }
      } catch (error) {
        console.error(`提取机场信息失败 (${subscription.name}):`, error);
      }
    }

    return { newCount, updatedCount };
  }

  // 从订阅名称和域名中提取机场名称
  private extractAirportNameFromSubscription(subscriptionName: string, domain: string): string {
    // 移除常见的后缀
    const cleanName = subscriptionName
      .replace(/\s*\(\d+个节点\)/, '')
      .replace(/\s*机场$/, '')
      .trim();

    if (cleanName && cleanName !== '未知机场') {
      return cleanName;
    }

    // 从域名提取
    const parts = domain.split('.');
    if (parts.length >= 2) {
      const mainDomain = parts[parts.length - 2];

      // 常见的机场域名映射
      const domainMappings: { [key: string]: string } = {
        'rixcloud': 'RixCloud',
        'nexitally': 'Nexitally',
        'dlercloud': 'DlerCloud',
        'tagss': 'TagSS',
        'ssrcloud': 'SSRCloud',
        'flyint': 'FlyInt',
        'maying': 'Maying',
        'ytoo': 'Ytoo',
        'sockboom': 'SockBoom',
        'justmysocks': 'JustMySocks',
        'dukou': 'DuKou'
      };

      const mappedName = domainMappings[mainDomain.toLowerCase()];
      if (mappedName) {
        return mappedName;
      }

      // 首字母大写
      return mainDomain.charAt(0).toUpperCase() + mainDomain.slice(1);
    }

    return domain;
  }

  // 迁移节点池配置表（添加新字段）
  private migrateNodePoolTable(): void {
    // 检查是否存在新字段
    this.db.all("PRAGMA table_info(nodepool_config)", [], (err, columns: any[]) => {
      if (err) {
        console.error('检查节点池表结构失败:', err);
        return;
      }

      const columnNames = columns.map(col => col.name);

      // 检查并添加 subConverter 字段
      if (!columnNames.includes('subConverter')) {
        this.db.run("ALTER TABLE nodepool_config ADD COLUMN subConverter TEXT NOT NULL DEFAULT 'SUBAPI.cmliussss.net'", (err) => {
          if (err) {
            console.error('添加 subConverter 字段失败:', err);
          } else {
            console.log('已添加 subConverter 字段');
          }
        });
      }

      // 检查并添加 subConfig 字段
      if (!columnNames.includes('subConfig')) {
        this.db.run("ALTER TABLE nodepool_config ADD COLUMN subConfig TEXT NOT NULL DEFAULT 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/config/ACL4SSR_Online_MultiCountry.ini'", (err) => {
          if (err) {
            console.error('添加 subConfig 字段失败:', err);
          } else {
            console.log('已添加 subConfig 字段');
          }
        });
      }

      // 检查并添加 token 字段
      if (!columnNames.includes('token')) {
        this.db.run("ALTER TABLE nodepool_config ADD COLUMN token TEXT NOT NULL DEFAULT 'auto'", (err) => {
          if (err) {
            console.error('添加 token 字段失败:', err);
          } else {
            console.log('已添加 token 字段');
          }
        });
      }

      // 检查并添加 guestToken 字段
      if (!columnNames.includes('guestToken')) {
        this.db.run("ALTER TABLE nodepool_config ADD COLUMN guestToken TEXT NOT NULL DEFAULT ''", (err) => {
          if (err) {
            console.error('添加 guestToken 字段失败:', err);
          } else {
            console.log('已添加 guestToken 字段');
          }
        });
      }

      // 检查并添加 subscriptionDays 字段
      if (!columnNames.includes('subscriptionDays')) {
        this.db.run("ALTER TABLE nodepool_config ADD COLUMN subscriptionDays INTEGER NOT NULL DEFAULT 30", (err) => {
          if (err) {
            console.error('添加 subscriptionDays 字段失败:', err);
          } else {
            console.log('已添加 subscriptionDays 字段');
          }
        });
      }

      // 检查并添加 subscriptionName 字段
      if (!columnNames.includes('subscriptionName')) {
        this.db.run("ALTER TABLE nodepool_config ADD COLUMN subscriptionName TEXT NOT NULL DEFAULT ''", (err) => {
          if (err) {
            console.error('添加 subscriptionName 字段失败:', err);
          } else {
            console.log('已添加 subscriptionName 字段');
          }
        });
      }

      // 检查并添加 botToken 字段
      if (!columnNames.includes('botToken')) {
        this.db.run("ALTER TABLE nodepool_config ADD COLUMN botToken TEXT NOT NULL DEFAULT ''", (err) => {
          if (err) {
            console.error('添加 botToken 字段失败:', err);
          } else {
            console.log('已添加 botToken 字段');
          }
        });
      }
    });
  }

  // 初始化节点池配置
  private initNodePoolConfig(): void {
    this.db.get('SELECT COUNT(*) as count FROM nodepool_config', [], (err, row: any) => {
      if (err) {
        console.error('检查节点池配置失败:', err);
        return;
      }

      if (row.count === 0) {
        // 插入默认配置
        const now = new Date().toISOString();
        const defaultConfig = {
          nodeLinks: '',
          subscriptionLinks: '',
          subscriptionName: '',
          totalTraffic: 99,
          expiryDate: '2099-12-31',
          updateInterval: 6,
          subConverter: 'SUBAPI.cmliussss.net',
          subConfig: 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/config/ACL4SSR_Online_MultiCountry.ini',
          token: 'auto',
          guestToken: '',
          subscriptionDays: 30
        };

        this.db.run(
          'INSERT INTO nodepool_config (nodeLinks, subscriptionLinks, subscriptionName, totalTraffic, expiryDate, updateInterval, subConverter, subConfig, token, guestToken, subscriptionDays, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          [defaultConfig.nodeLinks, defaultConfig.subscriptionLinks, defaultConfig.subscriptionName, defaultConfig.totalTraffic, defaultConfig.expiryDate, defaultConfig.updateInterval, defaultConfig.subConverter, defaultConfig.subConfig, defaultConfig.token, defaultConfig.guestToken, defaultConfig.subscriptionDays, now],
          (err) => {
            if (err) {
              console.error('插入默认节点池配置失败:', err);
            } else {
              console.log('初始化节点池配置成功');
            }
          }
        );
      }
    });
  }

  // 获取节点池配置
  async getNodePoolConfig(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.db.get('SELECT * FROM nodepool_config ORDER BY id DESC LIMIT 1', [], (err, row: any) => {
        if (err) {
          reject(err);
        } else if (row) {
          resolve({
            nodeLinks: row.nodeLinks || '',
            subscriptionLinks: row.subscriptionLinks || '',
            subscriptionName: row.subscriptionName || '',
            totalTraffic: row.totalTraffic || 99,
            expiryDate: row.expiryDate || '2099-12-31',
            updateInterval: row.updateInterval || 6,
            subConverter: row.subConverter || 'SUBAPI.cmliussss.net',
            subConfig: row.subConfig || 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/config/ACL4SSR_Online_MultiCountry.ini',
            token: row.token || 'auto',
            guestToken: row.guestToken || '',
            subscriptionDays: row.subscriptionDays || 30
          });
        } else {
          // 如果没有配置，返回默认值
          resolve({
            nodeLinks: '',
            subscriptionLinks: '',
            subscriptionName: '',
            totalTraffic: 99,
            expiryDate: '2099-12-31',
            updateInterval: 6,
            subConverter: 'SUBAPI.cmliussss.net',
            subConfig: 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/config/ACL4SSR_Online_MultiCountry.ini',
            token: 'auto',
            guestToken: '',
            subscriptionDays: 30
          });
        }
      });
    });
  }

  // 更新节点池配置
  async updateNodePoolConfig(config: any): Promise<void> {
    const now = new Date().toISOString();

    // 设置默认值
    const configWithDefaults = {
      nodeLinks: config.nodeLinks || '',
      subscriptionLinks: config.subscriptionLinks || '',
      totalTraffic: config.totalTraffic || 99,
      expiryDate: config.expiryDate || '2099-12-31',
      updateInterval: config.updateInterval || 6,
      subConverter: config.subConverter || 'SUBAPI.cmliussss.net',
      subConfig: config.subConfig || 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/config/ACL4SSR_Online_MultiCountry.ini',
      token: config.token || 'auto',
      guestToken: config.guestToken || '',
      subscriptionDays: config.subscriptionDays || 30,
      subscriptionName: config.subscriptionName || ''
    };

    console.log('更新节点池配置，处理后的数据:', configWithDefaults);

    return new Promise((resolve, reject) => {
      // 先检查是否存在配置
      this.db.get('SELECT id FROM nodepool_config ORDER BY id DESC LIMIT 1', [], (err, row: any) => {
        if (err) {
          console.error('查询节点池配置失败:', err);
          reject(err);
          return;
        }

        if (row) {
          // 更新现有配置
          console.log('更新现有节点池配置, ID:', row.id);
          this.db.run(
            'UPDATE nodepool_config SET nodeLinks = ?, subscriptionLinks = ?, subscriptionName = ?, totalTraffic = ?, expiryDate = ?, updateInterval = ?, subConverter = ?, subConfig = ?, token = ?, guestToken = ?, subscriptionDays = ?, updatedAt = ? WHERE id = ?',
            [
              configWithDefaults.nodeLinks,
              configWithDefaults.subscriptionLinks,
              configWithDefaults.subscriptionName,
              configWithDefaults.totalTraffic,
              configWithDefaults.expiryDate,
              configWithDefaults.updateInterval,
              configWithDefaults.subConverter,
              configWithDefaults.subConfig,
              configWithDefaults.token,
              configWithDefaults.guestToken,
              configWithDefaults.subscriptionDays,
              now,
              row.id
            ],
            function(err) {
              if (err) {
                console.error('更新节点池配置失败:', err);
                reject(err);
              } else {
                console.log('节点池配置更新成功');
                resolve();
              }
            }
          );
        } else {
          // 插入新配置
          console.log('插入新的节点池配置');
          this.db.run(
            'INSERT INTO nodepool_config (nodeLinks, subscriptionLinks, subscriptionName, totalTraffic, expiryDate, updateInterval, subConverter, subConfig, token, guestToken, subscriptionDays, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [
              configWithDefaults.nodeLinks,
              configWithDefaults.subscriptionLinks,
              configWithDefaults.subscriptionName,
              configWithDefaults.totalTraffic,
              configWithDefaults.expiryDate,
              configWithDefaults.updateInterval,
              configWithDefaults.subConverter,
              configWithDefaults.subConfig,
              configWithDefaults.token,
              configWithDefaults.guestToken,
              configWithDefaults.subscriptionDays,
              now
            ],
            function(err) {
              if (err) {
                console.error('插入节点池配置失败:', err);
                reject(err);
              } else {
                console.log('节点池配置插入成功');
                resolve();
              }
            }
          );
        }
      });
    });
  }

  close(): void {
    this.db.close();
  }
}
