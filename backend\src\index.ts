import express from 'express';
import cors from 'cors';
import { config, validateConfig } from './config';
import { errorHandler, notFoundHandler, asyncHandler, requestLogger } from './middleware/errorHandler';
import { Database } from './database';
import { SubscriptionParser } from './subscriptionParser';
import { SubscriptionService } from './services/SubscriptionService';
import { NodePoolManager } from './nodepool/NodePoolManager';
import { ApiResponse } from '../../shared/types';
import logger from './utils/logger';

// 验证配置
try {
  validateConfig();
  logger.info('配置验证成功');
} catch (error) {
  logger.error('配置验证失败', error);
  process.exit(1);
}

const app = express();
const db = new Database(config.database.path);
const parser = new SubscriptionParser();
const subscriptionService = new SubscriptionService(db, parser);

// 中间件
app.use(cors({
  origin: (origin, callback) => {
    // 如果配置为 * 则允许所有来源
    if (config.server.corsOrigin === '*') {
      callback(null, true);
      return;
    }
    
    // 如果没有origin（比如直接访问API），允许通过
    if (!origin) {
      callback(null, true);
      return;
    }
    
    // 支持多个origin，用逗号分隔
    const allowedOrigins = config.server.corsOrigin.split(',').map(o => o.trim());
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(null, false);
    }
  },
  credentials: true
}));
app.use(express.json());
app.use(requestLogger);

// 单例NodePoolManager缓存
let nodePoolManagerInstance: NodePoolManager | null = null;
let lastConfigSignature: string | null = null;

// 获取NodePoolManager实例（单例模式）
async function getNodePoolManager(): Promise<NodePoolManager> {
  try {
    const nodePoolConfig = await db.getNodePoolConfig();
    
    // 计算配置签名
    const configSignature = JSON.stringify({
      nodeLinks: nodePoolConfig.nodeLinks,
      subscriptionLinks: nodePoolConfig.subscriptionLinks,
      totalTraffic: nodePoolConfig.totalTraffic,
      expiryDate: nodePoolConfig.expiryDate,
      subscriptionDays: nodePoolConfig.subscriptionDays,
      updateInterval: nodePoolConfig.updateInterval
    });
    
    // 如果配置没有变化且实例存在，直接返回缓存的实例
    if (nodePoolManagerInstance && lastConfigSignature === configSignature) {
      return nodePoolManagerInstance;
    }
    
    // 创建新实例
    logger.nodePool('创建新实例');
    nodePoolManagerInstance = new NodePoolManager(nodePoolConfig);
    lastConfigSignature = configSignature;
    
    return nodePoolManagerInstance;
  } catch (error) {
    logger.error('获取NodePoolManager实例失败', error);
    throw error;
  }
}

// 强制清除NodePoolManager缓存（配置更新时调用）
function clearNodePoolManagerCache(): void {
  nodePoolManagerInstance = null;
  lastConfigSignature = null;
  logger.nodePool('清除缓存');
}

// ========== 认证相关路由 ==========

// 管理员认证端点
app.post('/api/admin/login', (req, res) => {
  const { password } = req.body;

  // 验证请求体
  if (!password) {
    const response: ApiResponse<null> = {
      success: false,
      message: '请提供密码'
    };
    return res.status(400).json(response);
  }

  if (password === config.server.adminPassword) {
    const response: ApiResponse<{ success: boolean }> = {
      success: true,
      data: { success: true },
      message: '登录成功'
    };
    logger.info('管理员登录成功');
    res.json(response);
  } else {
    const response: ApiResponse<null> = {
      success: false,
      message: '密码错误'
    };
    logger.warn('管理员登录失败 - 密码错误');
    res.status(401).json(response);
  }
});

// ========== 订阅相关路由 ==========

// 解析订阅链接获取信息
app.post('/api/admin/parse-subscription', asyncHandler(async (req, res) => {
  const { url } = req.body;
  const result = await subscriptionService.parseSubscription(url);
  
  const response: ApiResponse<typeof result.subscriptionInfo> = {
    success: true,
    data: result.subscriptionInfo,
    message: result.message
  };
  res.json(response);
}));

// 调试端点：测试订阅链接连通性
app.post('/api/admin/test-subscription', asyncHandler(async (req, res) => {
  const { url } = req.body;

  if (!url) {
    return res.status(400).json({ success: false, message: '请提供订阅链接' });
  }

  logger.debug('测试订阅链接', { url: subscriptionService['maskUrl'](url) });

  const axios = require('axios');
  const response = await axios.get(url, {
    timeout: config.subscription.timeout,
    headers: {
      'User-Agent': config.subscription.userAgent
    },
    validateStatus: () => true // 接受所有状态码
  });

  const result = {
    success: true,
    data: {
      status: response.status,
      headers: response.headers,
      contentLength: response.data?.length || 0,
      contentType: response.headers['content-type'],
      userInfo: response.headers['subscription-userinfo'] || response.headers['Subscription-Userinfo'],
      contentPreview: typeof response.data === 'string' ? response.data.substring(0, 200) : 'Binary data'
    }
  };

  logger.info('订阅链接测试完成', { status: response.status });
  res.json(result);
}));

// 获取所有订阅（用户前端）
app.get('/api/subscriptions', asyncHandler(async (_req, res) => {
  const userSubscriptions = await subscriptionService.getUserSubscriptions();
  
  const response: ApiResponse<typeof userSubscriptions> = {
    success: true,
    data: userSubscriptions
  };
  res.json(response);
}));

// 获取所有订阅（管理后台）
app.get('/api/admin/subscriptions', asyncHandler(async (_req, res) => {
  const subscriptions = await subscriptionService.getAllSubscriptions();
  
  const response: ApiResponse<typeof subscriptions> = {
    success: true,
    data: subscriptions
  };
  res.json(response);
}));

// 获取单个订阅
app.get('/api/admin/subscriptions/:id', asyncHandler(async (req, res) => {
  const subscription = await subscriptionService.getSubscriptionById(req.params.id);
  
  const response: ApiResponse<typeof subscription> = {
    success: true,
    data: subscription
  };
  res.json(response);
}));

// 创建订阅
app.post('/api/admin/subscriptions', asyncHandler(async (req, res) => {
  const subscription = await subscriptionService.createSubscription(req.body);
  
  const response: ApiResponse<typeof subscription> = {
    success: true,
    data: subscription,
    message: '订阅创建成功'
  };
  res.status(201).json(response);
}));

// 更新订阅
app.put('/api/admin/subscriptions/:id', asyncHandler(async (req, res) => {
  const subscription = await subscriptionService.updateSubscription(req.params.id, req.body);
  
  const response: ApiResponse<typeof subscription> = {
    success: true,
    data: subscription,
    message: '订阅更新成功'
  };
  res.json(response);
}));

// 删除订阅
app.delete('/api/admin/subscriptions/:id', asyncHandler(async (req, res) => {
  await subscriptionService.deleteSubscription(req.params.id);
  
  const response: ApiResponse<null> = {
    success: true,
    message: '订阅删除成功'
  };
  res.json(response);
}));

// 获取流量记录
app.get('/api/admin/traffic-records', async (req, res) => {
  try {
    const { subscriptionId, hours } = req.query;
    const records = await db.getTrafficRecords(
      subscriptionId as string,
      hours ? parseInt(hours as string) : undefined
    );

    const response: ApiResponse<typeof records> = {
      success: true,
      data: records,
      message: '获取流量记录成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '获取流量记录失败'
    };
    res.status(500).json(response);
  }
});

// 手动记录当前流量（旧方法，只记录数据库中的数据）
app.post('/api/admin/record-traffic', async (_req, res) => {
  try {
    await db.recordCurrentTraffic();

    const response: ApiResponse<null> = {
      success: true,
      message: '流量记录成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '记录流量失败'
    };
    res.status(500).json(response);
  }
});

// 智能记录流量（新方法，先刷新订阅再记录）
app.post('/api/admin/smart-record-traffic', async (_req, res) => {
  try {
    const result = await db.recordTrafficWithRefresh(parser);

    const response: ApiResponse<{ successCount: number; failureCount: number }> = {
      success: true,
      data: result,
      message: `智能流量记录完成：成功 ${result.successCount} 个，失败 ${result.failureCount} 个`
    };
    res.json(response);
  } catch (error) {
    console.error('智能流量记录失败:', error);
    const response: ApiResponse<null> = {
      success: false,
      message: '智能流量记录失败'
    };
    res.status(500).json(response);
  }
});

// 清理不完整的流量记录
app.post('/api/admin/cleanup-records', async (_req, res) => {
  try {
    await db.cleanupIncompleteRecords();

    const response: ApiResponse<null> = {
      success: true,
      message: '数据清理完成'
    };
    res.json(response);
  } catch (error) {
    console.error('数据清理失败:', error);
    const response: ApiResponse<null> = {
      success: false,
      message: '数据清理失败'
    };
    res.status(500).json(response);
  }
});

// 批量刷新所有订阅
app.post('/api/admin/refresh-all-subscriptions', async (_req, res) => {
  try {
    const subscriptions = await db.getAllSubscriptions();
    let successCount = 0;
    let failureCount = 0;
    const results: { id: string; name: string; success: boolean; error?: string }[] = [];

    console.log(`开始批量刷新 ${subscriptions.length} 个订阅...`);
    const startTime = Date.now();
    const overallTimeout = 3 * 60 * 1000; // 3分钟整体超时

    // 并发刷新所有订阅，但限制并发数量避免过载
    const batchSize = 3; // 减少并发数
    for (let i = 0; i < subscriptions.length; i += batchSize) {
      // 检查整体超时
      if (Date.now() - startTime > overallTimeout) {
        console.warn(`批量刷新超时，已处理 ${i} 个订阅，剩余 ${subscriptions.length - i} 个跳过`);
        // 为剩余订阅添加跳过记录
        for (let j = i; j < subscriptions.length; j++) {
          results.push({
            id: subscriptions[j].id,
            name: subscriptions[j].name,
            success: false,
            error: '整体操作超时，跳过处理'
          });
          failureCount++;
        }
        break;
      }

      const batch = subscriptions.slice(i, i + batchSize);

      await Promise.all(batch.map(async (subscription) => {
        const subscriptionTimeout = 10000; // 10秒单个订阅超时

        try {
          console.log(`刷新订阅: ${subscription.name}`);

          // 使用Promise.race实现超时控制
          const parsePromise = parser.parseSubscription(subscription.url);
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('订阅解析超时')), subscriptionTimeout);
          });

          const parsedInfo = await Promise.race([parsePromise, timeoutPromise]) as any;

          // 更新订阅信息
          const updateData = {
            totalTraffic: parsedInfo.totalTraffic || subscription.totalTraffic,
            usedTraffic: parsedInfo.usedTraffic || 0,
            status: parsedInfo.status || 'active',
            ...(parsedInfo.expiryDate && { expiryDate: parsedInfo.expiryDate })
          };

          await db.updateSubscription(subscription.id, updateData);

          results.push({
            id: subscription.id,
            name: subscription.name,
            success: true
          });
          successCount++;
          console.log(`✓ 刷新订阅 ${subscription.name} 成功`);
        } catch (error) {
          console.error(`✗ 刷新订阅 ${subscription.name} 失败:`, (error as any).message || error);

          // 解析失败时，将状态设置为错误状态
          try {
            await db.updateSubscription(subscription.id, { status: 'error' as const });
          } catch (updateError) {
            console.error(`更新订阅 ${subscription.name} 状态失败:`, updateError);
          }

          results.push({
            id: subscription.id,
            name: subscription.name,
            success: false,
            error: error instanceof Error ? error.message : '未知错误'
          });
          failureCount++;
        }
      }));

      // 批次间短暂延迟
      if (i + batchSize < subscriptions.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    console.log(`批量刷新完成：成功 ${successCount} 个，失败 ${failureCount} 个，耗时 ${duration} 秒`);

    const response: ApiResponse<{ successCount: number; failureCount: number; results: typeof results }> = {
      success: true,
      data: { successCount, failureCount, results },
      message: `批量刷新完成：成功 ${successCount} 个，失败 ${failureCount} 个，耗时 ${duration} 秒`
    };
    res.json(response);
  } catch (error) {
    console.error('批量刷新订阅失败:', error);
    const response: ApiResponse<null> = {
      success: false,
      message: '批量刷新订阅失败'
    };
    res.status(500).json(response);
  }
});

// 获取系统配置
app.get('/api/admin/system-config', async (_req, res) => {
  try {
    const systemConfig = await db.getSystemConfig();
    const response: ApiResponse<typeof systemConfig> = {
      success: true,
      data: systemConfig,
      message: '获取系统配置成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '获取系统配置失败'
    };
    res.status(500).json(response);
  }
});

// 更新系统配置
app.put('/api/admin/system-config', async (req, res) => {
  try {
    const { recordInterval } = req.body;

    if (!recordInterval || recordInterval < 1 || recordInterval > 1440) {
      const response: ApiResponse<null> = {
        success: false,
        message: '记录间隔必须在1-1440分钟之间'
      };
      return res.status(400).json(response);
    }

    await db.updateSystemConfig({ recordInterval });

    // 立即更新定时任务，不等待下次检查
    if (updateRecordingInterval) {
      try {
        await updateRecordingInterval();
        console.log(`配置更新后立即应用新的记录间隔：${recordInterval}分钟`);
      } catch (updateError) {
        console.error('立即更新定时任务失败:', updateError);
      }
    }

    const response: ApiResponse<null> = {
      success: true,
      message: '系统配置更新成功，新的记录间隔已立即生效'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '更新系统配置失败'
    };
    res.status(500).json(response);
  }
});

// 修改管理员密码
app.post('/api/admin/change-password', async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      const response: ApiResponse<null> = {
        success: false,
        message: '请提供当前密码和新密码'
      };
      return res.status(400).json(response);
    }

    // 验证当前密码
    if (currentPassword !== config.server.adminPassword) {
      const response: ApiResponse<null> = {
        success: false,
        message: '当前密码错误'
      };
      return res.status(401).json(response);
    }

    if (newPassword.length < 6) {
      const response: ApiResponse<null> = {
        success: false,
        message: '新密码长度至少6位'
      };
      return res.status(400).json(response);
    }

    // 注意：这里只是演示，实际生产环境中应该将密码保存到环境变量或配置文件
    // 由于这是演示项目，我们只在内存中更新密码
    (config.server as any).adminPassword = newPassword;

    const response: ApiResponse<null> = {
      success: true,
      message: '密码修改成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '密码修改失败'
    };
    res.status(500).json(response);
  }
});

// 手动更新智能记录间隔
app.post('/api/admin/update-recording-interval', async (_req, res) => {
  try {
    if (updateRecordingInterval) {
      await updateRecordingInterval();
      const systemConfig = await db.getSystemConfig();
      const response: ApiResponse<{ currentInterval: number }> = {
        success: true,
        data: { currentInterval: systemConfig.recordInterval },
        message: `智能记录间隔已更新为 ${systemConfig.recordInterval} 分钟`
      };
      res.json(response);
    } else {
      const response: ApiResponse<null> = {
        success: false,
        message: '定时任务未初始化'
      };
      res.status(500).json(response);
    }
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '更新智能记录间隔失败'
    };
    res.status(500).json(response);
  }
});

// 获取机场列表
app.get('/api/admin/airports', async (_req, res) => {
  try {
    const airports = await db.getAllAirports();
    const response: ApiResponse<typeof airports> = {
      success: true,
      data: airports,
      message: '获取机场列表成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '获取机场列表失败'
    };
    res.status(500).json(response);
  }
});

// 创建机场信息
app.post('/api/admin/airports', async (req, res) => {
  try {
    const { name, domain, website, description } = req.body;

    if (!name || !domain) {
      const response: ApiResponse<null> = {
        success: false,
        message: '机场名称和域名为必填项'
      };
      return res.status(400).json(response);
    }

    const airport = await db.createAirport({ name, domain, website, description });

    const response: ApiResponse<typeof airport> = {
      success: true,
      data: airport,
      message: '机场信息创建成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: error instanceof Error ? error.message : '创建机场信息失败'
    };
    res.status(500).json(response);
  }
});

// 更新机场信息
app.put('/api/admin/airports/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, domain, website, description } = req.body;

    const airport = await db.updateAirport(id, { name, domain, website, description });

    const response: ApiResponse<typeof airport> = {
      success: true,
      data: airport,
      message: '机场信息更新成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: error instanceof Error ? error.message : '更新机场信息失败'
    };
    res.status(500).json(response);
  }
});

// 删除机场信息
app.delete('/api/admin/airports/:id', async (req, res) => {
  try {
    const { id } = req.params;
    await db.deleteAirport(id);

    const response: ApiResponse<null> = {
      success: true,
      message: '机场信息删除成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: error instanceof Error ? error.message : '删除机场信息失败'
    };
    res.status(500).json(response);
  }
});

// 从订阅中提取机场信息
app.post('/api/admin/extract-airports', async (_req, res) => {
  try {
    const result = await db.extractAirportsFromSubscriptions();

    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
      message: `机场信息提取完成：新增 ${result.newCount} 个，更新 ${result.updatedCount} 个`
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      message: '提取机场信息失败'
    };
    res.status(500).json(response);
  }
});

// 节点池配置管理
app.get('/api/admin/nodepool/config', async (_req, res) => {
  try {
    const config = await db.getNodePoolConfig();
    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('获取节点池配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取节点池配置失败'
    });
  }
});

app.put('/api/admin/nodepool/config', async (req, res) => {
  try {
    console.log('收到更新节点池配置请求:', req.body);
    
    await db.updateNodePoolConfig(req.body);
    
    // 清除NodePoolManager缓存，确保下次获取时重新创建
    clearNodePoolManagerCache();
    console.log('已清除NodePoolManager缓存');
    
    res.json({
      success: true,
      message: '节点池配置更新成功'
    });
  } catch (error) {
    console.error('更新节点池配置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新节点池配置失败'
    });
  }
});

// 获取原始节点列表
app.get('/api/nodepool/raw', async (req, res) => {
  try {
    const token = req.query.token as string;
    const config = await db.getNodePoolConfig();
    
    // 验证token - 修复空字符串的问题
    const validTokens = [config.token];
    if (config.guestToken && config.guestToken.trim()) {
      validTokens.push(config.guestToken);
    }
    
    if (!token || !validTokens.includes(token)) {
      return res.status(401).json({
        success: false,
        message: '访问令牌无效'
      });
    }

    const nodePoolManager = await getNodePoolManager();
    
    // 获取所有节点
    const nodes = await nodePoolManager.getAllNodes();
    const validNodeUrls = nodes.filter(node => node.isValid).map(node => node.url);
    
    // 获取流量信息
    const trafficInfo = nodePoolManager.getTrafficInfo();

    res.set({
      'Content-Type': 'text/plain; charset=utf-8',
      'Profile-Update-Interval': `${config.updateInterval}`,
      'Subscription-Userinfo': `upload=${trafficInfo.upload}; download=${trafficInfo.download}; total=${trafficInfo.total}; expire=${trafficInfo.expire}`,
    });

    res.send(validNodeUrls.join('\n'));
  } catch (error) {
    console.error('获取原始节点列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取原始节点列表失败'
    });
  }
});

// 生成订阅
app.get('/api/nodepool/sub', async (req, res) => {
  try {
    const token = req.query.token as string;
    const format = req.query.format as string || 'base64';
    const config = await db.getNodePoolConfig();
    
    // 验证token - 修复空字符串的问题
    const validTokens = [config.token];
    if (config.guestToken && config.guestToken.trim()) {
      validTokens.push(config.guestToken);
    }
    
    if (!token || !validTokens.includes(token)) {
      return res.status(401).json({
        success: false,
        message: '访问令牌无效'
      });
    }

    const nodePoolManager = await getNodePoolManager();
    
    // 检查是否过期
    if (nodePoolManager.isExpired()) {
      const expiredMessage = "订阅已过期，请联系管理员续费";
      const trafficInfo = nodePoolManager.getTrafficInfo();
      
      res.set({
        'Content-Type': 'text/plain; charset=utf-8',
        'Profile-Update-Interval': `${config.updateInterval}`,
        'Subscription-Userinfo': `upload=${trafficInfo.upload}; download=${trafficInfo.download}; total=${trafficInfo.total}; expire=${trafficInfo.expire}`,
      });
      
      return res.send(Buffer.from(expiredMessage).toString('base64'));
    }

    // 生成订阅内容
    let subscription;
    try {
      subscription = await nodePoolManager.generateSubscription(format);
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: `不支持的订阅格式: ${format}`
      });
    }

    // 获取流量信息
    const trafficInfo = nodePoolManager.getTrafficInfo();

    // 设置响应头
    res.set({
      'Content-Type': format === 'clash' ? 'text/yaml; charset=utf-8' : 'text/plain; charset=utf-8',
      'Profile-Update-Interval': `${config.updateInterval}`,
      'Subscription-Userinfo': `upload=${trafficInfo.upload}; download=${trafficInfo.download}; total=${trafficInfo.total}; expire=${trafficInfo.expire}`,
      'Content-Disposition': `attachment; filename="${config.token || 'subscription'}.${format === 'clash' ? 'yaml' : 'txt'}"`
    });

    res.send(subscription.content);
  } catch (error) {
    console.error('生成订阅失败:', error);
    res.status(500).json({
      success: false,
      message: '生成订阅失败'
    });
  }
});

// 节点池测试页面
app.get('/nodepool/test', async (req, res) => {
  try {
    const config = await db.getNodePoolConfig();
    const token = config.token || 'auto';
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    
    const nodePoolManager = await getNodePoolManager();
    const stats = await nodePoolManager.getStats();
    const cacheStatus = nodePoolManager.getCacheStatus();
    
    const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>节点池测试页面</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .link { color: #007bff; text-decoration: none; }
            .link:hover { text-decoration: underline; }
            .stats { background: #f8f9fa; }
            .cache { background: #e3f2fd; }
        </style>
    </head>
    <body>
        <h1>节点池测试页面</h1>
        
        <div class="section stats">
            <h2>统计信息</h2>
            <p>总节点数: ${stats.totalNodes}</p>
            <p>有效节点数: ${stats.validNodes}</p>
            <p>节点源数量: ${stats.sourceCount}</p>
            <p>最后更新: ${stats.lastUpdate.toLocaleString()}</p>
        </div>
        
        <div class="section cache">
            <h2>缓存状态</h2>
            <p>缓存有效: ${cacheStatus.isValid ? '是' : '否'}</p>
            <p>下次更新: ${cacheStatus.nextUpdate.toLocaleString()}</p>
            <p>缓存节点数: ${cacheStatus.nodeCount}</p>
        </div>
        
        <div class="section">
            <h2>订阅链接</h2>
            <p><strong>自动格式:</strong><br>
            <a href="${baseUrl}/api/nodepool/sub?token=${token}" target="_blank" class="link">
            ${baseUrl}/api/nodepool/sub?token=${token}
            </a></p>
            
            <p><strong>Base64格式:</strong><br>
            <a href="${baseUrl}/api/nodepool/sub?token=${token}&format=base64" target="_blank" class="link">
            ${baseUrl}/api/nodepool/sub?token=${token}&format=base64
            </a></p>
            
            <p><strong>Clash格式:</strong><br>
            <a href="${baseUrl}/api/nodepool/sub?token=${token}&format=clash" target="_blank" class="link">
            ${baseUrl}/api/nodepool/sub?token=${token}&format=clash
            </a></p>
            
            <p><strong>原始节点:</strong><br>
            <a href="${baseUrl}/api/nodepool/raw?token=${token}" target="_blank" class="link">
            ${baseUrl}/api/nodepool/raw?token=${token}
            </a></p>
            
            <p><strong>调试信息:</strong><br>
            <a href="${baseUrl}/api/nodepool/debug?token=${token}" target="_blank" class="link">
            ${baseUrl}/api/nodepool/debug?token=${token}
            </a></p>
            
            <p><strong>配置验证:</strong><br>
            <a href="${baseUrl}/api/nodepool/validate?token=${token}" target="_blank" class="link">
            ${baseUrl}/api/nodepool/validate?token=${token}
            </a></p>
        </div>
    </body>
    </html>
    `;
    
    res.send(html);
  } catch (error) {
    console.error('生成测试页面失败:', error);
    res.status(500).send('生成测试页面失败');
  }
});

// 调试信息
app.get('/api/nodepool/debug', async (req, res) => {
  try {
    const token = req.query.token as string;
    const config = await db.getNodePoolConfig();
    
    // 验证token - 修复空字符串的问题
    const validTokens = [config.token];
    if (config.guestToken && config.guestToken.trim()) {
      validTokens.push(config.guestToken);
    }
    
    if (!token || !validTokens.includes(token)) {
      console.log(`Token验证失败: 提供的token="${token}", 有效tokens:`, validTokens);
      return res.status(401).json({
        success: false,
        message: '访问令牌无效'
      });
    }

    const nodePoolManager = await getNodePoolManager();
    // 在调试信息中启用详细日志
    const debugInfo = await nodePoolManager.generateDebugInfo();

    res.json({
      success: true,
      data: debugInfo
    });
  } catch (error) {
    console.error('获取调试信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取调试信息失败'
    });
  }
});

// 配置验证
app.get('/api/nodepool/validate', async (req, res) => {
  try {
    const token = req.query.token as string;
    const config = await db.getNodePoolConfig();
    
    // 验证token - 修复空字符串的问题
    const validTokens = [config.token];
    if (config.guestToken && config.guestToken.trim()) {
      validTokens.push(config.guestToken);
    }
    
    if (!token || !validTokens.includes(token)) {
      console.log(`Token验证失败: 提供的token="${token}", 有效tokens:`, validTokens);
      return res.status(401).json({
        success: false,
        message: '访问令牌无效'
      });
    }

    const nodePoolManager = await getNodePoolManager();
    const validation = await nodePoolManager.validateConfig();

    // 生成测试订阅
    let testSubscription: {
      format: string;
      nodeCount: number;
      contentLength: number;
    } | null = null;
    try {
      const subscription = await nodePoolManager.generateSubscription('base64');
      testSubscription = {
        format: subscription.format,
        nodeCount: subscription.nodeCount,
        contentLength: subscription.content.length
      };
    } catch (error) {
      console.error('生成测试订阅失败:', error);
    }

    res.json({
      success: true,
      data: {
        validation,
        testSubscription,
        links: {
          base64: `${req.protocol}://${req.get('host')}/api/nodepool/sub?token=${token}&format=base64`,
          auto: `${req.protocol}://${req.get('host')}/api/nodepool/sub?token=${token}`,
          raw: `${req.protocol}://${req.get('host')}/api/nodepool/raw?token=${token}`
        }
      }
    });
  } catch (error) {
    console.error('验证配置失败:', error);
    res.status(500).json({
      success: false,
      message: '验证配置失败'
    });
  }
});

// 错误处理中间件（必须在所有路由之后）
app.use(notFoundHandler);
app.use(errorHandler);

// 定时任务：根据配置的时间间隔刷新订阅并记录流量数据
let recordingInterval: NodeJS.Timeout | null = null;
let currentIntervalMinutes = 60; // 记录当前使用的间隔

// 导出更新定时任务的函数，供API调用
let updateRecordingInterval: (() => Promise<void>) | null = null;

const startTrafficRecording = async () => {
  const smartRecord = async () => {
    try {
      console.log('开始智能流量记录...');
      const result = await db.recordTrafficWithRefresh(parser);
      console.log(`智能流量记录完成：成功 ${result.successCount} 个，失败 ${result.failureCount} 个`);
    } catch (error) {
      console.error('智能流量记录失败，使用备用方案:', error);
      // 备用方案：只记录当前数据库中的数据
      try {
        await db.recordCurrentTraffic();
        console.log('备用流量记录完成');
      } catch (recordError) {
        console.error('备用流量记录也失败:', recordError);
      }
    }
  };

  const updateRecordingIntervalInternal = async () => {
    try {
      // 清除现有定时器
      if (recordingInterval) {
        clearInterval(recordingInterval);
      }

      // 获取当前配置的时间间隔
      const systemConfig = await db.getSystemConfig();
      const intervalMinutes = systemConfig.recordInterval;
      const intervalMs = intervalMinutes * 60 * 1000;

      // 更新当前间隔记录
      currentIntervalMinutes = intervalMinutes;

      // 设置新的定时器
      recordingInterval = setInterval(smartRecord, intervalMs);

      console.log(`智能流量记录定时任务已更新，每 ${intervalMinutes} 分钟自动刷新订阅并记录最新流量`);
    } catch (error) {
      console.error('更新定时任务失败，使用默认间隔:', error);
      // 使用默认间隔（60分钟）
      if (recordingInterval) {
        clearInterval(recordingInterval);
      }
      currentIntervalMinutes = 60;
      recordingInterval = setInterval(smartRecord, 60 * 60 * 1000);
      console.log('使用默认间隔：每60分钟执行一次智能流量记录');
    }
  };

  // 导出更新函数供API调用
  updateRecordingInterval = updateRecordingIntervalInternal;

  // 立即执行一次记录
  await smartRecord();

  // 设置定时任务
  await updateRecordingIntervalInternal();

  // 每1分钟检查一次配置是否有变化，如有变化则更新定时器
  setInterval(async () => {
    try {
      const currentConfig = await db.getSystemConfig();

      // 检查配置的间隔是否与当前使用的间隔不同
      if (currentConfig.recordInterval !== currentIntervalMinutes) {
        console.log(`检测到配置变化：${currentIntervalMinutes}分钟 -> ${currentConfig.recordInterval}分钟，更新定时任务...`);
        await updateRecordingIntervalInternal();
      }
    } catch (error) {
      console.error('检查配置变化失败:', error);
    }
  }, 1 * 60 * 1000); // 每1分钟检查一次，确保配置变化能快速生效
};

// 启动服务器
app.listen(config.server.port, () => {
  logger.info(`服务器启动成功`);
  logger.info(`地址: http://localhost:${config.server.port}`);
  logger.info(`环境: ${config.nodeEnv}`);
  logger.info(`数据库: ${config.database.path}`);

  // 启动流量记录定时任务
  startTrafficRecording();
});

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到 SIGTERM，正在关闭服务器...');
  db.close();
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('收到 SIGINT，正在关闭服务器...');
  db.close();
  process.exit(0);
});
