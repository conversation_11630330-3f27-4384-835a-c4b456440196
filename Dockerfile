# 使用Node.js基础镜像
FROM node:18-alpine

WORKDIR /app

# 创建数据目录
RUN mkdir -p /app/data

# 复制构建产物和依赖配置
COPY backend/dist ./backend/dist
COPY backend/package*.json ./backend/
COPY frontend/dist ./frontend/dist

# 安装后端生产依赖
RUN cd backend && npm install --omit=dev && npm cache clean --force

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 设置权限
RUN chown -R nodejs:nodejs /app
USER nodejs

# 暴露端口
EXPOSE 3001

# 设置环境变量
ENV NODE_ENV=production
ENV DATABASE_PATH=/app/data/subscriptions.db

# 启动命令
CMD ["node", "backend/dist/backend/src/index.js"] 