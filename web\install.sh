#!/bin/bash

# CentOS系统 - 机场订阅管理系统一键安装脚本

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "                    🚀 机场订阅管理系统 - CentOS一键安装                      "
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo -e "${NC}\n"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ 请使用root用户运行此安装脚本${NC}"
    echo -e "${YELLOW}   sudo ./install.sh${NC}"
    exit 1
fi

# 检查CentOS版本
if [ -f /etc/centos-release ]; then
    CENTOS_VERSION=$(cat /etc/centos-release | grep -oE '[0-9]+' | head -1)
    echo -e "${GREEN}✅ 检测到CentOS ${CENTOS_VERSION}${NC}"
else
    echo -e "${YELLOW}⚠️  未检测到CentOS系统，继续安装...${NC}"
fi

# 更新系统
echo -e "${YELLOW}📦 更新系统包...${NC}"
yum update -y

# 安装必要的工具
echo -e "${YELLOW}🔧 安装系统工具...${NC}"
yum install -y curl wget net-tools

# 安装Node.js
echo -e "${YELLOW}📥 安装Node.js 18.x...${NC}"
if ! command -v node &> /dev/null; then
    curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
    yum install -y nodejs
    
    if command -v node &> /dev/null; then
        echo -e "${GREEN}✅ Node.js安装成功: $(node --version)${NC}"
        echo -e "${GREEN}✅ npm版本: $(npm --version)${NC}"
    else
        echo -e "${RED}❌ Node.js安装失败${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Node.js已存在: $(node --version)${NC}"
fi

# 配置防火墙
echo -e "${YELLOW}🔥 配置防火墙...${NC}"
if command -v firewall-cmd &> /dev/null; then
    firewall-cmd --permanent --add-port=3001/tcp
    firewall-cmd --reload
    echo -e "${GREEN}✅ 防火墙已开放3001端口${NC}"
elif command -v iptables &> /dev/null; then
    iptables -I INPUT -p tcp --dport 3001 -j ACCEPT
    service iptables save 2>/dev/null
    echo -e "${GREEN}✅ iptables已开放3001端口${NC}"
fi

# 创建应用用户
echo -e "${YELLOW}👤 创建应用用户...${NC}"
if ! id "reslms" &>/dev/null; then
    useradd -r -s /bin/bash -d /opt/reslms reslms
    echo -e "${GREEN}✅ 创建用户reslms成功${NC}"
else
    echo -e "${GREEN}✅ 用户reslms已存在${NC}"
fi

# 创建应用目录
echo -e "${YELLOW}📁 设置应用目录...${NC}"
mkdir -p /opt/reslms
chown -R reslms:reslms /opt/reslms

# 设置权限
echo -e "${YELLOW}🔐 设置文件权限...${NC}"
chmod +x start.sh
chmod +x install.sh

echo -e "${GREEN}"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "                            🎉 安装完成！                                     "
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo -e "${NC}"

echo -e "${BLUE}📋 下一步操作:${NC}"
echo -e "${BLUE}   1. 将应用文件复制到 /opt/reslms/${NC}"
echo -e "${BLUE}   2. 切换到reslms用户: sudo su - reslms${NC}"
echo -e "${BLUE}   3. 启动应用: ./start.sh${NC}"
echo ""
echo -e "${BLUE}🌐 访问地址:${NC}"
echo -e "${BLUE}   http://your-server-ip:3001${NC}"
echo ""
echo -e "${YELLOW}💡 提示: 记得在云服务器控制台开放3001端口${NC}"
echo "" 