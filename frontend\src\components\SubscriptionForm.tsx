import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { adminApi } from '../api';
import { Subscription, CreateSubscriptionRequest, UpdateSubscriptionRequest } from '../types';

interface SubscriptionFormProps {
  onSuccess: (subscription: Subscription) => void;
}

export default function SubscriptionForm({ onSuccess }: SubscriptionFormProps) {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = !!id;

  const [formData, setFormData] = useState({
    name: '',
    url: '',
    totalTraffic: '',
    expiryDate: '',
  });

  // 存储解析到的流量信息（只读显示）
  const [parsedTrafficInfo, setParsedTrafficInfo] = useState<{
    totalTraffic?: number;
    usedTraffic?: number;
    remainingTraffic?: number;
    status?: string;
  } | null>(null);

  const [loading, setLoading] = useState(false);
  const [parsing, setParsing] = useState(false);
  const [testing, setTesting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  useEffect(() => {
    if (isEdit && id) {
      loadSubscription(id);
    }
  }, [isEdit, id]);

  const loadSubscription = async (subscriptionId: string) => {
    try {
      setLoading(true);
      const subscription = await adminApi.getSubscription(subscriptionId);
      setFormData({
        name: subscription.name,
        url: subscription.url || '',
        totalTraffic: subscription.totalTraffic.toString(),
        expiryDate: subscription.expiryDate ? subscription.expiryDate.split('T')[0] : '',
      });

      // 在编辑模式下显示当前的流量信息
      setParsedTrafficInfo({
        totalTraffic: subscription.totalTraffic,
        usedTraffic: subscription.usedTraffic,
        remainingTraffic: subscription.remainingTraffic,
        status: subscription.status
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      if (isEdit && id) {
        const updateData: UpdateSubscriptionRequest = {
          name: formData.name,
          url: formData.url,
          totalTraffic: parseFloat(formData.totalTraffic),
          expiryDate: formData.expiryDate ? new Date(formData.expiryDate).toISOString() : undefined,
        };
        const subscription = await adminApi.updateSubscription(id, updateData);
        onSuccess(subscription);
      } else {
        const totalTraffic = parseFloat(formData.totalTraffic);
        const createData: CreateSubscriptionRequest = {
          name: formData.name,
          url: formData.url,
          totalTraffic,
          usedTraffic: parsedTrafficInfo?.usedTraffic || 0,
          remainingTraffic: parsedTrafficInfo?.remainingTraffic !== undefined
            ? parsedTrafficInfo.remainingTraffic
            : totalTraffic,
          status: (parsedTrafficInfo?.status || 'active') as 'active' | 'expired' | 'suspended' | 'error',
          expiryDate: new Date(formData.expiryDate).toISOString(),
        };
        const subscription = await adminApi.createSubscription(createData);
        onSuccess(subscription);
      }
      navigate('/admin/subscriptions');
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // 清除消息
    if (error) setError(null);
    if (successMessage) setSuccessMessage(null);

    // 如果修改了URL，清除之前解析的流量信息
    if (name === 'url') {
      setParsedTrafficInfo(null);
    }
  };

  // 格式化流量显示
  const formatTraffic = (gb: number) => {
    if (gb >= 1024) {
      return `${(gb / 1024).toFixed(1)} TB`;
    }
    return `${gb.toFixed(1)} GB`;
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'expired':
        return 'text-red-600 bg-red-100';
      case 'suspended':
        return 'text-yellow-600 bg-yellow-100';
      case 'error':
        return 'text-orange-600 bg-orange-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '正常';
      case 'expired':
        return '已过期';
      case 'suspended':
        return '已暂停';
      case 'error':
        return '解析失败';
      default:
        return '未知';
    }
  };

  const handleParseSubscription = async () => {
    if (!formData.url) {
      setError('请先输入订阅链接');
      return;
    }

    setParsing(true);
    setError(null);

    try {
      const parsedInfo = await adminApi.parseSubscription(formData.url);

      // 更新表单数据
      setFormData(prev => ({
        ...prev,
        name: parsedInfo.name || prev.name,
        totalTraffic: parsedInfo.totalTraffic?.toString() || prev.totalTraffic,
        expiryDate: parsedInfo.expiryDate ? parsedInfo.expiryDate.split('T')[0] : prev.expiryDate,
      }));

      // 保存流量信息用于显示
      setParsedTrafficInfo({
        totalTraffic: parsedInfo.totalTraffic,
        usedTraffic: parsedInfo.usedTraffic,
        remainingTraffic: parsedInfo.remainingTraffic,
        status: parsedInfo.status
      });

      // 根据解析结果显示不同的消息
      if (parsedInfo.status === 'error') {
        setError('订阅链接解析失败，但已提取基本信息。请手动填写流量和到期时间。');
      } else {
        setSuccessMessage('订阅信息获取成功！已自动填充表单。');
      }
    } catch (err) {
      // 解析完全失败时，设置错误状态
      setParsedTrafficInfo({
        status: 'error'
      });
      setError(err instanceof Error ? err.message : '解析订阅失败');
    } finally {
      setParsing(false);
    }
  };

  const handleTestSubscription = async () => {
    if (!formData.url) {
      setError('请先输入订阅链接');
      return;
    }

    setTesting(true);
    setError(null);
    setDebugInfo(null);

    try {
      const testResult = await adminApi.testSubscription(formData.url);
      setDebugInfo(testResult);
      setSuccessMessage('订阅链接测试成功！请查看调试信息。');
    } catch (err) {
      setError(err instanceof Error ? err.message : '测试失败');
    } finally {
      setTesting(false);
    }
  };

  if (loading && isEdit) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          {isEdit ? '编辑订阅' : '添加订阅'}
        </h2>
        <p className="text-gray-600">
          {isEdit ? '修改订阅配置信息' : '通过订阅链接自动获取并创建新的机场订阅'}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主表单区域 */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            {/* 消息提示 */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-400 rounded-r-lg">
                <div className="flex items-center">
                  <span className="text-red-400 mr-2">❌</span>
                  <div className="text-red-700">{error}</div>
                </div>
              </div>
            )}

            {successMessage && (
              <div className="mb-6 p-4 bg-green-50 border-l-4 border-green-400 rounded-r-lg">
                <div className="flex items-center">
                  <span className="text-green-400 mr-2">✅</span>
                  <div className="text-green-700">{successMessage}</div>
                </div>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-8">
              {/* 步骤1: 订阅链接 */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                  <h3 className="text-lg font-semibold text-gray-900">订阅链接</h3>
                </div>

                <div>
                  <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-2">
                    机场订阅链接 *
                  </label>
                  <div className="relative">
                    <input
                      type="url"
                      id="url"
                      name="url"
                      value={formData.url}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      placeholder="https://example.com/api/v1/client/subscribe?token=..."
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <span className="text-gray-400">🔗</span>
                    </div>
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    粘贴机场提供的订阅链接，系统将自动解析订阅信息
                  </p>
                </div>

                <div className="flex flex-wrap gap-3">
                  <button
                    type="button"
                    onClick={handleParseSubscription}
                    disabled={parsing || !formData.url}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                  >
                    {parsing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                        <span>解析中...</span>
                      </>
                    ) : (
                      <>
                        <span>🚀</span>
                        <span>自动获取信息</span>
                      </>
                    )}
                  </button>

                  <button
                    type="button"
                    onClick={handleTestSubscription}
                    disabled={testing || !formData.url}
                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                  >
                    {testing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-600 border-t-transparent"></div>
                        <span>测试中...</span>
                      </>
                    ) : (
                      <>
                        <span>🔍</span>
                        <span>测试连接</span>
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* 步骤2: 基本信息 */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                  <h3 className="text-lg font-semibold text-gray-900">基本信息</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      订阅名称 *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      placeholder="例如：高速机场A"
                    />
                  </div>

                  <div>
                    <label htmlFor="totalTraffic" className="block text-sm font-medium text-gray-700 mb-2">
                      总流量 (GB) *
                    </label>
                    <input
                      type="number"
                      id="totalTraffic"
                      name="totalTraffic"
                      value={formData.totalTraffic}
                      onChange={handleChange}
                      required
                      min="0"
                      step="0.1"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      placeholder="100"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="expiryDate" className="block text-sm font-medium text-gray-700 mb-2">
                    到期时间 *
                  </label>
                  <input
                    type="date"
                    id="expiryDate"
                    name="expiryDate"
                    value={formData.expiryDate}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                  <p className="mt-2 text-sm text-gray-500">
                    订阅状态将根据到期时间和流量使用情况自动判断
                  </p>
                </div>
              </div>

              {/* 表单操作按钮 */}
              <div className="flex justify-end space-x-4 pt-8 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => navigate('/admin/subscriptions')}
                  className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 transition-colors"
                  disabled={loading}
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      <span>保存中...</span>
                    </>
                  ) : (
                    <>
                      <span>{isEdit ? '💾' : '✨'}</span>
                      <span>{isEdit ? '更新订阅' : '创建订阅'}</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* 侧边栏 - 信息展示和帮助 */}
        <div className="lg:col-span-1 space-y-6">
          {/* 解析到的流量信息 */}
          {parsedTrafficInfo && (
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                <span className="mr-2">📊</span>
                {isEdit ? '当前流量状态' : '解析结果'}
              </h4>

              <div className="space-y-4">
                <div className="bg-white rounded-lg p-4 border border-blue-100">
                  <div className="text-sm text-blue-600 mb-1">总流量</div>
                  <div className="text-2xl font-bold text-blue-900">
                    {parsedTrafficInfo.totalTraffic ? formatTraffic(parsedTrafficInfo.totalTraffic) : '未知'}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-white rounded-lg p-3 border border-blue-100">
                    <div className="text-xs text-blue-600 mb-1">已使用</div>
                    <div className="text-lg font-bold text-orange-600">
                      {parsedTrafficInfo.usedTraffic ? formatTraffic(parsedTrafficInfo.usedTraffic) : '0 GB'}
                    </div>
                  </div>
                  <div className="bg-white rounded-lg p-3 border border-blue-100">
                    <div className="text-xs text-blue-600 mb-1">剩余</div>
                    <div className="text-lg font-bold text-green-600">
                      {parsedTrafficInfo.remainingTraffic !== undefined ? formatTraffic(parsedTrafficInfo.remainingTraffic) : '未知'}
                    </div>
                  </div>
                </div>

                {/* 流量使用进度条 */}
                {parsedTrafficInfo.totalTraffic && parsedTrafficInfo.usedTraffic !== undefined && (
                  <div>
                    <div className="flex justify-between text-xs text-blue-600 mb-2">
                      <span>使用进度</span>
                      <span>
                        {((parsedTrafficInfo.usedTraffic / parsedTrafficInfo.totalTraffic) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="w-full bg-blue-200 rounded-full h-3">
                      <div
                        className={`h-3 rounded-full transition-all duration-500 ${
                          parsedTrafficInfo.usedTraffic > parsedTrafficInfo.totalTraffic
                            ? 'bg-red-500'
                            : parsedTrafficInfo.usedTraffic / parsedTrafficInfo.totalTraffic > 0.8
                            ? 'bg-yellow-500'
                            : 'bg-blue-500'
                        }`}
                        style={{
                          width: `${Math.min(100, (parsedTrafficInfo.usedTraffic / parsedTrafficInfo.totalTraffic) * 100)}%`,
                        }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* 状态显示 */}
                {parsedTrafficInfo.status && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-blue-600">状态</span>
                    <span
                      className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                        parsedTrafficInfo.status
                      )}`}
                    >
                      {getStatusText(parsedTrafficInfo.status)}
                    </span>
                  </div>
                )}

                {isEdit && (
                  <button
                    type="button"
                    onClick={handleParseSubscription}
                    disabled={parsing || !formData.url}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
                  >
                    {parsing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                        <span>刷新中...</span>
                      </>
                    ) : (
                      <>
                        <span>🔄</span>
                        <span>刷新流量信息</span>
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          )}

          {/* 调试信息 */}
          {debugInfo && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                  <span className="mr-2">🔍</span>
                  调试信息
                </h4>
                <button
                  type="button"
                  onClick={() => setDebugInfo(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">状态码:</span>
                  <span className="font-mono">{debugInfo.status}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">内容长度:</span>
                  <span className="font-mono">{debugInfo.contentLength} 字节</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">内容类型:</span>
                  <span className="font-mono text-xs">{debugInfo.contentType || '未知'}</span>
                </div>
                {debugInfo.userInfo && (
                  <div>
                    <span className="text-gray-600">用户信息:</span>
                    <code className="block mt-1 p-2 bg-gray-100 rounded text-xs break-all">
                      {debugInfo.userInfo}
                    </code>
                  </div>
                )}
                {debugInfo.contentPreview && (
                  <details className="mt-3">
                    <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                      查看内容预览
                    </summary>
                    <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto max-h-32 border">
                      {debugInfo.contentPreview}
                    </pre>
                  </details>
                )}
              </div>
            </div>
          )}

          {/* 帮助信息 */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span className="mr-2">💡</span>
              使用提示
            </h4>
            <div className="space-y-3 text-sm text-gray-600">
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-0.5">•</span>
                <span>粘贴机场订阅链接后，点击"自动获取信息"来解析订阅详情</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-0.5">•</span>
                <span>系统会自动获取流量使用情况，创建时将保存当前的实际流量数据</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-0.5">•</span>
                <span>如果解析失败，可以使用"测试连接"查看详细信息</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500 mt-0.5">•</span>
                <span>订阅状态会根据到期时间和流量使用情况自动判断</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
