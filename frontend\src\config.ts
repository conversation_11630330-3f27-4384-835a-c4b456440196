// 应用配置
export const config = {
  // API 基础地址
  API_BASE_URL: '/api',

  // 应用信息
  APP_NAME: '机场订阅管理系统',
  APP_VERSION: '1.0.0',

  // 本地存储键名
  STORAGE_KEYS: {
    ADMIN_AUTH: 'admin_authenticated'
  },

  // UI配置
  THEME: {
    PRIMARY_COLOR: 'gray',
    LAYOUT: 'minimal'
  },

  // 开发配置
  IS_DEVELOPMENT: (import.meta as any).env?.DEV || false,
  IS_PRODUCTION: (import.meta as any).env?.PROD || false,

  // 安全配置
  SECURITY: {
    // 注意：生产环境中应该移除前端密码验证，改为后端JWT验证
    ENABLE_FRONTEND_AUTH: (import.meta as any).env?.DEV || false,
    SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24小时
  }
} as const;

// API配置 - 使用相对路径，让nginx代理处理
export const API_CONFIG = {
  BASE_URL: '/api',  // 使用相对路径，通过nginx代理
  TIMEOUT: 10000,
  ENDPOINTS: {
    SUBSCRIPTIONS: '/api/subscriptions',
    ADMIN: '/api/admin',
    LOGIN: '/api/admin/login',
    PARSE_SUBSCRIPTION: '/api/admin/parse-subscription',
    TEST_SUBSCRIPTION: '/api/admin/test-subscription',
    TRAFFIC_RECORDS: '/api/admin/traffic-records',
    RECORD_TRAFFIC: '/api/admin/record-traffic',
    SMART_RECORD_TRAFFIC: '/api/admin/smart-record-traffic',
    CLEANUP_RECORDS: '/api/admin/cleanup-records',
    REFRESH_ALL: '/api/admin/refresh-all-subscriptions',
    SYSTEM_CONFIG: '/api/admin/system-config',
    CHANGE_PASSWORD: '/api/admin/change-password',
    UPDATE_RECORDING_INTERVAL: '/api/admin/update-recording-interval',
    AIRPORTS: '/api/admin/airports',
    EXTRACT_AIRPORTS: '/api/admin/extract-airports',
  }
};

// 环境配置
export const ENV_CONFIG = {
  IS_DEVELOPMENT: (import.meta as any).env?.DEV || false,
  IS_PRODUCTION: (import.meta as any).env?.PROD || false,
  
  // 功能开关
  FEATURES: {
    ENABLE_FRONTEND_AUTH: (import.meta as any).env?.DEV || false,
    ENABLE_ANALYTICS: true,
    ENABLE_NODE_POOL: true,
  }
};
