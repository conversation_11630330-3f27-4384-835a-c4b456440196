import { useState, useEffect, useCallback } from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import { adminApi } from '../api';
import { Subscription } from '../types';
import SubscriptionList from './SubscriptionList';
import SubscriptionForm from './SubscriptionForm';

export default function AdminSubscriptions() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const loadSubscriptions = useCallback(async () => {
    try {
      setLoading(true);
      const data = await adminApi.getSubscriptions();
      setSubscriptions(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载失败');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadSubscriptions();
  }, [loadSubscriptions]);

  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这个订阅吗？')) {
      return;
    }

    try {
      await adminApi.deleteSubscription(id);
      setSubscriptions(prev => prev.filter(sub => sub.id !== id));
    } catch (err) {
      alert(err instanceof Error ? err.message : '删除失败');
    }
  };

  const handleCreate = async (subscription: Subscription) => {
    setSubscriptions(prev => [subscription, ...prev]);
    navigate('/admin/subscriptions');
  };

  const handleUpdate = async (subscription: Subscription) => {
    setSubscriptions(prev =>
      prev.map(sub => sub.id === subscription.id ? subscription : sub)
    );
    navigate('/admin/subscriptions');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={loadSubscriptions}
          className="px-4 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-200"
        >
          重试
        </button>
      </div>
    );
  }

  return (
    <Routes>
      <Route
        path="/"
        element={
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">管理订阅</h1>
                <p className="text-gray-500 mt-1">添加、编辑和管理所有订阅</p>
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={loadSubscriptions}
                  className="px-4 py-2 bg-white text-gray-600 text-sm rounded-lg hover:bg-gray-50 hover:text-gray-800 focus:outline-none transition-colors duration-200 flex items-center space-x-2 border border-gray-200"
                >
                  <span>🔄</span>
                  <span>刷新</span>
                </button>
                <button
                  onClick={() => navigate('/admin/subscriptions/new')}
                  className="px-4 py-2 bg-gray-900 text-white text-sm rounded-lg hover:bg-gray-800 focus:outline-none transition-colors duration-200 flex items-center space-x-2"
                >
                  <span>➕</span>
                  <span>添加订阅</span>
                </button>
              </div>
            </div>
            <SubscriptionList
              subscriptions={subscriptions}
              onDelete={handleDelete}
              onUpdate={loadSubscriptions}
            />
          </div>
        }
      />
      <Route
        path="/new"
        element={
          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/admin/subscriptions')}
                className="px-3 py-2 bg-white text-gray-600 text-sm rounded-lg hover:bg-gray-100 hover:text-gray-900 focus:outline-none transition-colors duration-200 flex items-center space-x-2 border border-gray-200"
              >
                <span>←</span>
                <span>返回</span>
              </button>
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">添加订阅</h1>
                <p className="text-gray-500 mt-1">创建新的订阅配置</p>
              </div>
            </div>
            <SubscriptionForm onSuccess={handleCreate} />
          </div>
        }
      />
      <Route
        path="/edit/:id"
        element={
          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/admin/subscriptions')}
                className="px-3 py-2 bg-white text-gray-600 text-sm rounded-lg hover:bg-gray-100 hover:text-gray-900 focus:outline-none transition-colors duration-200 flex items-center space-x-2 border border-gray-200"
              >
                <span>←</span>
                <span>返回</span>
              </button>
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">编辑订阅</h1>
                <p className="text-gray-500 mt-1">修改订阅配置信息</p>
              </div>
            </div>
            <SubscriptionForm onSuccess={handleUpdate} />
          </div>
        }
      />
    </Routes>
  );
}
