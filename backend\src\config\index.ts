import 'dotenv/config';

interface DatabaseConfig {
  path: string;
  connectionPoolSize?: number;
  queryTimeout?: number;
}

interface ServerConfig {
  port: number;
  corsOrigin: string;
  adminPassword: string;
}

interface SubscriptionConfig {
  timeout: number;
  retryAttempts: number;
  userAgent: string;
}

interface AppConfig {
  database: DatabaseConfig;
  server: ServerConfig;
  subscription: SubscriptionConfig;
  nodeEnv: 'development' | 'production' | 'test';
}

const config: AppConfig = {
  database: {
    path: process.env.DATABASE_PATH || './subscriptions.db',
    connectionPoolSize: parseInt(process.env.DB_POOL_SIZE || '10'),
    queryTimeout: parseInt(process.env.DB_TIMEOUT || '5000')
  },
  server: {
    port: parseInt(process.env.PORT || '3001'),
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    adminPassword: process.env.ADMIN_PASSWORD || 'admin123456'
  },
  subscription: {
    timeout: parseInt(process.env.SUBSCRIPTION_TIMEOUT || '8000'),
    retryAttempts: parseInt(process.env.SUBSCRIPTION_RETRY || '3'),
    userAgent: process.env.USER_AGENT || 'ClashforWindows/0.20.39'
  },
  nodeEnv: (process.env.NODE_ENV as 'development' | 'production' | 'test') || 'development'
};

export function validateConfig(): void {
  const requiredKeys = [
    'database.path',
    'server.port',
    'server.corsOrigin',
    'server.adminPassword'
  ];

  for (const key of requiredKeys) {
    const value = key.split('.').reduce((obj, k) => obj?.[k], config as any);
    if (value === undefined || value === null || value === '') {
      throw new Error(`配置项 ${key} 未设置或为空`);
    }
  }

  // 端口范围验证
  if (config.server.port < 1 || config.server.port > 65535) {
    throw new Error('端口号必须在 1-65535 范围内');
  }

  // 密码强度验证
  if (config.server.adminPassword.length < 6) {
    console.warn('⚠️  警告：管理员密码长度不足6位，建议使用更强的密码');
  }
}

export { config };
export type { AppConfig, DatabaseConfig, ServerConfig, SubscriptionConfig };
