import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { config } from '../config';
import { adminApi } from '../api';

interface AuthContextType {
  isAuthenticated: boolean;
  login: (password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // 检查本地存储中的登录状态和时间戳
    const authStatus = localStorage.getItem(config.STORAGE_KEYS.ADMIN_AUTH);
    const authTimestamp = localStorage.getItem(config.STORAGE_KEYS.ADMIN_AUTH + '_timestamp');

    if (authStatus === 'true' && authTimestamp) {
      const loginTime = parseInt(authTimestamp);
      const now = Date.now();

      // 检查是否超过会话超时时间
      if (now - loginTime < config.SECURITY.SESSION_TIMEOUT) {
        setIsAuthenticated(true);
      } else {
        // 会话过期，清除认证状态
        logout();
      }
    }
  }, []);

  const login = async (password: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      // 使用后端API验证
      const success = await adminApi.login(password);
      if (success) {
        setIsAuthenticated(true);
        const timestamp = Date.now().toString();
        localStorage.setItem(config.STORAGE_KEYS.ADMIN_AUTH, 'true');
        localStorage.setItem(config.STORAGE_KEYS.ADMIN_AUTH + '_timestamp', timestamp);
        return true;
      }
      return false;
    } catch (error) {
      console.error('登录失败:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setIsAuthenticated(false);
    localStorage.removeItem(config.STORAGE_KEYS.ADMIN_AUTH);
    localStorage.removeItem(config.STORAGE_KEYS.ADMIN_AUTH + '_timestamp');
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
