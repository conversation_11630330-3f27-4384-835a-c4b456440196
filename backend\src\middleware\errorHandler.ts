import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../../../shared/types';

// 自定义错误类
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;

  constructor(message: string, statusCode: number = 500, code?: string) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.isOperational = true;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

// 验证错误类
export class ValidationError extends AppError {
  constructor(message: string, field?: string) {
    super(message, 400, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
    if (field) {
      this.message = `${field}: ${message}`;
    }
  }
}

// 数据库错误类
export class DatabaseError extends AppError {
  constructor(message: string, originalError?: Error) {
    super(`数据库操作失败: ${message}`, 500, 'DATABASE_ERROR');
    this.name = 'DatabaseError';
    if (originalError) {
      this.stack = originalError.stack;
    }
  }
}

// 网络请求错误类
export class NetworkError extends AppError {
  constructor(message: string, statusCode: number = 500) {
    super(`网络请求失败: ${message}`, statusCode, 'NETWORK_ERROR');
    this.name = 'NetworkError';
  }
}

// 异步处理器包装函数
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 全局错误处理中间件
export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  console.error(`错误发生在 ${req.method} ${req.path}:`, error);

  // 如果响应已经发送，则交给默认错误处理器
  if (res.headersSent) {
    return next(error);
  }

  let statusCode = 500;
  let message = '服务器内部错误';
  let code = 'INTERNAL_ERROR';

  // 处理自定义错误
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
    code = error.code || 'APP_ERROR';
  } 
  // 处理数据库相关错误
  else if (error.message.includes('SQLITE_')) {
    statusCode = 500;
    message = '数据库操作失败';
    code = 'DATABASE_ERROR';
  }
  // 处理网络超时错误
  else if (error.message.includes('timeout') || error.message.includes('ECONNRESET')) {
    statusCode = 408;
    message = '请求超时，请稍后重试';
    code = 'TIMEOUT_ERROR';
  }
  // 处理权限错误
  else if (error.message.includes('permission') || error.message.includes('unauthorized')) {
    statusCode = 403;
    message = '访问被拒绝';
    code = 'PERMISSION_ERROR';
  }
  // 处理JSON解析错误
  else if (error instanceof SyntaxError && error.message.includes('JSON')) {
    statusCode = 400;
    message = '请求数据格式错误';
    code = 'JSON_PARSE_ERROR';
  }

  // 开发环境下返回详细错误信息
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const response: ApiResponse<null> = {
    success: false,
    message,
    ...(isDevelopment && {
      error: {
        code,
        stack: error.stack,
        details: error.message
      }
    })
  };

  res.status(statusCode).json(response);
};

// 404 处理中间件
export const notFoundHandler = (req: Request, res: Response): void => {
  const response: ApiResponse<null> = {
    success: false,
    message: `路径 ${req.originalUrl} 不存在`
  };
  
  res.status(404).json(response);
};

// 请求日志中间件
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const { method, originalUrl } = req;
    const { statusCode } = res;
    
    console.log(`${method} ${originalUrl} ${statusCode} - ${duration}ms`);
  });
  
  next();
};

// 速率限制错误
export class RateLimitError extends AppError {
  constructor(message: string = '请求频率过高，请稍后重试') {
    super(message, 429, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
  }
}

// 输入验证辅助函数
export const validateRequired = (value: any, fieldName: string): void => {
  if (value === undefined || value === null || value === '') {
    throw new ValidationError(`${fieldName} 是必填字段`);
  }
};

export const validateUrl = (url: string, fieldName: string = 'URL'): void => {
  try {
    new URL(url);
  } catch {
    throw new ValidationError(`${fieldName} 格式不正确`, fieldName);
  }
};

export const validateNumber = (value: any, fieldName: string, min?: number, max?: number): void => {
  if (isNaN(Number(value))) {
    throw new ValidationError(`${fieldName} 必须是数字`, fieldName);
  }
  
  const num = Number(value);
  if (min !== undefined && num < min) {
    throw new ValidationError(`${fieldName} 不能小于 ${min}`, fieldName);
  }
  if (max !== undefined && num > max) {
    throw new ValidationError(`${fieldName} 不能大于 ${max}`, fieldName);
  }
};
