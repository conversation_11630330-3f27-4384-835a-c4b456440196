#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 启动机场订阅管理系统...${NC}\n"

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo -e "${YELLOW}⚠️  警告: 不建议使用root用户运行应用${NC}"
    echo -e "${YELLOW}   建议创建专用用户运行此应用${NC}\n"
fi

# 检查Node.js是否已安装
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到Node.js${NC}"
    echo -e "${RED}   请先安装Node.js 16.0.0或更高版本${NC}"
    echo -e "${BLUE}   CentOS安装命令:${NC}"
    echo -e "${BLUE}   curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -${NC}"
    echo -e "${BLUE}   sudo yum install -y nodejs${NC}"
    exit 1
fi

# 检查Node.js版本
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo -e "${RED}❌ 错误: Node.js版本过低${NC}"
    echo -e "${RED}   当前版本: $(node --version)${NC}"
    echo -e "${RED}   需要版本: 16.0.0或更高${NC}"
    exit 1
fi

# 检查npm是否可用
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到npm${NC}"
    echo -e "${RED}   npm通常与Node.js一起安装${NC}"
    exit 1
fi

# 检查并安装依赖
if [ ! -d "backend/node_modules" ]; then
    echo -e "${YELLOW}📦 安装生产依赖...${NC}"
    cd backend
    if npm install --production; then
        echo -e "${GREEN}✅ 依赖安装成功${NC}"
    else
        echo -e "${RED}❌ 依赖安装失败！${NC}"
        exit 1
    fi
    cd ..
fi

# 检查端口是否被占用
PORT=${PORT:-3001}
if netstat -tlnp 2>/dev/null | grep -q ":${PORT} "; then
    echo -e "${YELLOW}⚠️  端口 ${PORT} 已被占用${NC}"
    echo -e "${YELLOW}   请使用 PORT=其他端口号 ./start.sh 指定其他端口${NC}"
    echo -e "${YELLOW}   或停止占用端口的进程${NC}"
    exit 1
fi

# 设置文件权限
chmod +x start.sh 2>/dev/null

echo -e "${GREEN}"
echo "🎉 启动服务器..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📱 前端界面: http://localhost:${PORT}"
echo "🔧 管理后台: http://localhost:${PORT}/admin"
echo "⚙️  API接口: http://localhost:${PORT}/api"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo -e "${NC}"
echo -e "${BLUE}💡 提示: 按 Ctrl+C 停止服务${NC}"
echo -e "${BLUE}💡 后台运行: nohup ./start.sh > app.log 2>&1 &${NC}"
echo ""

# 启动应用
node start.js 