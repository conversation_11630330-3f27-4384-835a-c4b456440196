version: '3.8'

services:
  frontend:
    image: nginx:alpine
    container_name: airport-subscription-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    volumes:
      - ./frontend/dist:/usr/share/nginx/html:ro
      - ./frontend-nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - backend
    networks:
      - app-network

  backend:
    build: .
    container_name: airport-subscription-backend
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - ADMIN_PASSWORD=admin123456
      - JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
      - SESSION_SECRET=your-session-secret-at-least-32-characters-long
      - CORS_ORIGIN=*
      - DATABASE_PATH=/app/data/subscriptions.db
      - LOG_LEVEL=info
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/api/subscriptions"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: airport-subscription-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./main-nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - frontend
      - backend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  app-data:
    driver: local 