# ⚡ CentOS快速开始指南

## 🎯 3分钟快速部署

### 📋 准备工作
- CentOS 7+ 服务器
- root权限访问
- 可以访问互联网

### 🚀 一键部署

```bash
# 1. 下载并解压部署包
unzip 机场订阅管理系统-CentOS部署包.zip
cd web

# 2. 运行一键安装 (自动安装Node.js、配置防火墙等)
chmod +x install.sh
sudo ./install.sh

# 3. 复制文件到系统目录
sudo cp -r ./* /opt/reslms/
sudo chown -R reslms:reslms /opt/reslms

# 4. 启动应用
sudo su - reslms
cd /opt/reslms
chmod +x start.sh
./start.sh
```

## 🌐 访问应用

**浏览器访问**: http://your-server-ip:3001

- **用户前端**: http://your-server-ip:3001
- **管理后台**: http://your-server-ip:3001/admin
- **默认密码**: admin123

## 🔧 三种启动方式

### 1. 直接启动 (开发/测试)
```bash
./start.sh
```

### 2. 后台运行
```bash
nohup ./start.sh > app.log 2>&1 &
```

### 3. 系统服务 (生产推荐)
```bash
# 配置服务
sudo cp reslms.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable reslms
sudo systemctl start reslms

# 管理服务
sudo systemctl status reslms    # 查看状态
sudo systemctl stop reslms     # 停止服务
sudo systemctl restart reslms  # 重启服务
```

## 🔥 防火墙配置

### 自动配置 (推荐)
运行 `install.sh` 会自动配置防火墙

### 手动配置
```bash
# CentOS 7+
sudo firewall-cmd --permanent --add-port=3001/tcp
sudo firewall-cmd --reload

# CentOS 6
sudo iptables -I INPUT -p tcp --dport 3001 -j ACCEPT
sudo service iptables save
```

## 📱 云服务器配置

记得在云服务器控制台开放3001端口：

| 云平台 | 操作位置 |
|--------|----------|
| 阿里云 | ECS控制台 → 安全组 → 配置规则 |
| 腾讯云 | CVM控制台 → 安全组 → 入站规则 |
| 华为云 | ECS控制台 → 安全组 → 入方向规则 |

## 🛠️ 文件说明

| 文件 | 说明 | 用途 |
|------|------|------|
| `start.sh` | Linux启动脚本 | 直接启动应用 |
| `install.sh` | 一键安装脚本 | 安装Node.js和依赖 |
| `reslms.service` | 系统服务配置 | systemd服务管理 |
| `CentOS部署指南.md` | 详细部署文档 | 完整部署说明 |

## 🔧 常见问题

### Q: 无法访问网站
```bash
# 检查服务状态
sudo systemctl status reslms

# 检查端口是否开放
sudo firewall-cmd --list-ports

# 检查进程是否运行
ps aux | grep node
```

### Q: 端口被占用
```bash
# 查看端口占用
sudo netstat -tlnp | grep 3001

# 修改端口
PORT=8080 ./start.sh
```

### Q: 权限错误
```bash
# 修复权限
sudo chown -R reslms:reslms /opt/reslms
sudo chmod +x /opt/reslms/start.sh
```

## 📞 获取帮助

1. 查看详细文档: `CentOS部署指南.md`
2. 查看系统日志: `sudo journalctl -u reslms -f`
3. 查看应用日志: `tail -f app.log`

---

**🎉 部署成功！开始使用您的机场订阅管理系统吧！** 