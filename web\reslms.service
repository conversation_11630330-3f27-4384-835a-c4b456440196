[Unit]
Description=机场订阅管理系统
Documentation=https://github.com/reslms/reslms
After=network.target
Wants=network.target

[Service]
Type=simple
User=reslms
Group=reslms
WorkingDirectory=/opt/reslms
ExecStart=/usr/bin/node start.js
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
Environment=NODE_ENV=production
Environment=PORT=3001

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=reslms

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/reslms

# 进程限制
LimitNOFILE=65536
LimitNPROC=65536

[Install]
WantedBy=multi-user.target 