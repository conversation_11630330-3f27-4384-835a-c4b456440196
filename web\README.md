# 机场订阅管理系统 - 生产部署包

## 📋 包含文件

```
web/
├── start.js              # Node.js启动脚本 (跨平台)
├── start.bat             # Windows启动脚本
├── README.md             # 本说明文件
└── backend/
    ├── package.json      # 生产依赖配置
    └── dist/             # 编译后的代码
        ├── src/          # 后端服务代码
        ├── public/       # 前端静态文件
        └── shared/       # 共享类型定义
```

## 🚀 部署步骤

### 1. 上传文件
将整个 `web` 文件夹上传到服务器

### 2. 安装依赖
```bash
cd backend
npm install --production
```

### 3. 启动服务

**Linux/Mac:**
```bash
node ../start.js
```

**Windows:**
```bash
start.bat
```

## 🌐 访问地址

- **前端界面**: http://localhost:3001
- **管理后台**: http://localhost:3001/admin
- **API接口**: http://localhost:3001/api

## ⚙️ 环境配置

### 自定义端口
```bash
# Linux/Mac
PORT=8080 node start.js

# Windows
set PORT=8080 && node start.js
```

### 环境变量
- `PORT`: 服务端口 (默认: 3001)
- `NODE_ENV`: 运行环境 (默认: production)

## 📋 系统要求

- **Node.js**: 16.0.0 或更高版本
- **内存**: 最少 256MB
- **存储**: 最少 100MB 可用空间

## 🔧 常见问题

### Q: 端口被占用
A: 使用 `PORT=其他端口号 node start.js` 更改端口

### Q: 启动失败
A: 
1. 检查 Node.js 版本是否符合要求
2. 确保已运行 `npm install --production`
3. 检查文件权限

### Q: 数据库问题
A: 数据库文件会自动创建在 `backend/` 目录下

## 🛡️ 安全建议

1. **修改默认密码**: 首次登录后立即修改管理员密码
2. **防火墙配置**: 只开放必要的端口
3. **HTTPS配置**: 生产环境建议配置SSL证书
4. **定期备份**: 备份 SQLite 数据库文件

## 📞 技术支持

如有问题，请检查：
1. Node.js版本是否正确
2. 依赖是否安装完整
3. 端口是否可用
4. 文件权限是否正确 