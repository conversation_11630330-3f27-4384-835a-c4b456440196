#!/usr/bin/env node

console.log('🚀 启动机场订阅管理系统...\n');

// 检查Node.js版本
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 16) {
    console.error('❌ 错误: 需要 Node.js 16.0.0 或更高版本');
    console.error(`   当前版本: ${nodeVersion}`);
    console.error('   请访问 https://nodejs.org/ 下载最新版本');
    process.exit(1);
}

// 启动应用
require('./backend/dist/src/index.js');

console.log('✅ 服务器启动完成!');
console.log('📱 前端界面: http://localhost:3001');
console.log('🔧 管理后台: http://localhost:3001/admin');
console.log('⚙️  API接口: http://localhost:3001/api');
console.log('�� 按 Ctrl+C 停止服务\n'); 