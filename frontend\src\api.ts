import axios from 'axios';
import { Subscription, CreateSubscriptionRequest, UpdateSubscriptionRequest, ApiResponse } from './types';

const api = axios.create({
  baseURL: '/api',  // 使用相对路径，通过nginx代理
  timeout: 10000,
});

// 为长时间运行的操作创建单独的API实例
const longRunningApi = axios.create({
  baseURL: '/api',  // 使用相对路径，通过nginx代理
  timeout: 6 * 60 * 1000, // 6分钟超时
});

// 用户前端API
export const subscriptionApi = {
  // 获取订阅列表（用户视图）
  getSubscriptions: async (): Promise<Subscription[]> => {
    const response = await api.get<ApiResponse<Subscription[]>>('/subscriptions');
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '获取订阅列表失败');
  },
};

// 订阅解析信息接口
export interface ParsedSubscriptionInfo {
  name?: string;
  totalTraffic?: number;
  usedTraffic?: number;
  remainingTraffic?: number;
  expiryDate?: string;
  status?: 'active' | 'expired' | 'suspended' | 'error';
}

// 管理员API
export const adminApi = {
  // 管理员登录
  login: async (password: string): Promise<boolean> => {
    try {
      const response = await api.post<ApiResponse<{ success: boolean }>>('/admin/login', { password });
      return response.data.success && response.data.data?.success === true;
    } catch (error: any) {
      if (error.response?.status === 401) {
        // 401错误是预期的认证失败
        return false;
      }
      // 其他错误（如网络错误）则抛出
      throw error;
    }
  },

  // 解析订阅链接获取信息
  parseSubscription: async (url: string): Promise<ParsedSubscriptionInfo> => {
    const response = await api.post<ApiResponse<ParsedSubscriptionInfo>>('/admin/parse-subscription', { url });
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '解析订阅失败');
  },

  // 测试订阅链接连通性
  testSubscription: async (url: string): Promise<any> => {
    const response = await api.post<ApiResponse<any>>('/admin/test-subscription', { url });
    if (response.data.success) {
      return response.data.data;
    }
    throw new Error(response.data.message || '测试失败');
  },

  // 获取所有订阅（管理员视图）
  getSubscriptions: async (): Promise<Subscription[]> => {
    const response = await api.get<ApiResponse<Subscription[]>>('/admin/subscriptions');
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '获取订阅列表失败');
  },

  // 获取单个订阅
  getSubscription: async (id: string): Promise<Subscription> => {
    const response = await api.get<ApiResponse<Subscription>>(`/admin/subscriptions/${id}`);
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '获取订阅失败');
  },

  // 创建订阅
  createSubscription: async (data: CreateSubscriptionRequest): Promise<Subscription> => {
    const response = await api.post<ApiResponse<Subscription>>('/admin/subscriptions', data);
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '创建订阅失败');
  },

  // 更新订阅
  updateSubscription: async (id: string, data: UpdateSubscriptionRequest): Promise<Subscription> => {
    const response = await api.put<ApiResponse<Subscription>>(`/admin/subscriptions/${id}`, data);
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '更新订阅失败');
  },

  // 删除订阅
  deleteSubscription: async (id: string): Promise<void> => {
    const response = await api.delete<ApiResponse<null>>(`/admin/subscriptions/${id}`);
    if (!response.data.success) {
      throw new Error(response.data.message || '删除订阅失败');
    }
  },

  // 刷新订阅信息
  refreshSubscription: async (id: string): Promise<Subscription> => {
    const response = await api.post<ApiResponse<Subscription>>(`/admin/subscriptions/${id}/refresh`);
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '刷新订阅失败');
  },

  // 获取流量记录
  getTrafficRecords: async (subscriptionId?: string, hours?: number): Promise<any[]> => {
    const params = new URLSearchParams();
    if (subscriptionId) params.append('subscriptionId', subscriptionId);
    if (hours) params.append('hours', hours.toString());

    const response = await api.get<ApiResponse<any[]>>(`/admin/traffic-records?${params}`);
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '获取流量记录失败');
  },

  // 手动记录当前流量
  recordTraffic: async (): Promise<void> => {
    const response = await api.post<ApiResponse<null>>('/admin/record-traffic');
    if (!response.data.success) {
      throw new Error(response.data.message || '记录流量失败');
    }
  },

  // 批量刷新所有订阅
  refreshAllSubscriptions: async (): Promise<{ successCount: number; failureCount: number; results: any[] }> => {
    const response = await longRunningApi.post<ApiResponse<{ successCount: number; failureCount: number; results: any[] }>>('/admin/refresh-all-subscriptions');
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '批量刷新订阅失败');
  },

  // 智能记录流量（先刷新订阅再记录）
  smartRecordTraffic: async (): Promise<{ successCount: number; failureCount: number }> => {
    const response = await longRunningApi.post<ApiResponse<{ successCount: number; failureCount: number }>>('/admin/smart-record-traffic');
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '智能流量记录失败');
  },

  // 清理不完整的流量记录
  cleanupRecords: async (): Promise<void> => {
    const response = await api.post<ApiResponse<null>>('/admin/cleanup-records');
    if (!response.data.success) {
      throw new Error(response.data.message || '数据清理失败');
    }
  },

  // 获取系统配置
  getSystemConfig: async (): Promise<{ recordInterval: number }> => {
    const response = await api.get<ApiResponse<{ recordInterval: number }>>('/admin/system-config');
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '获取系统配置失败');
  },

  // 更新系统配置
  updateSystemConfig: async (config: { recordInterval: number }): Promise<void> => {
    const response = await api.put<ApiResponse<null>>('/admin/system-config', config);
    if (!response.data.success) {
      throw new Error(response.data.message || '更新系统配置失败');
    }
  },

  // 手动更新智能记录间隔
  updateRecordingInterval: async (): Promise<{ currentInterval: number }> => {
    const response = await api.post<ApiResponse<{ currentInterval: number }>>('/admin/update-recording-interval');
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '更新智能记录间隔失败');
  },

  // 修改管理员密码
  changePassword: async (currentPassword: string, newPassword: string): Promise<void> => {
    const response = await api.post<ApiResponse<null>>('/admin/change-password', {
      currentPassword,
      newPassword
    });
    if (!response.data.success) {
      throw new Error(response.data.message || '密码修改失败');
    }
  },

  // 获取机场列表
  getAirports: async (): Promise<any[]> => {
    const response = await api.get<ApiResponse<any[]>>('/admin/airports');
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '获取机场列表失败');
  },

  // 创建机场信息
  createAirport: async (data: { name: string; domain: string; website?: string; description?: string }): Promise<any> => {
    const response = await api.post<ApiResponse<any>>('/admin/airports', data);
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '创建机场信息失败');
  },

  // 更新机场信息
  updateAirport: async (id: string, data: { name?: string; domain?: string; website?: string; description?: string }): Promise<any> => {
    const response = await api.put<ApiResponse<any>>(`/admin/airports/${id}`, data);
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '更新机场信息失败');
  },

  // 删除机场信息
  deleteAirport: async (id: string): Promise<void> => {
    const response = await api.delete<ApiResponse<null>>(`/admin/airports/${id}`);
    if (!response.data.success) {
      throw new Error(response.data.message || '删除机场信息失败');
    }
  },

  // 从订阅中提取机场信息
  extractAirportsFromSubscriptions: async (): Promise<{ newCount: number; updatedCount: number }> => {
    const response = await api.post<ApiResponse<{ newCount: number; updatedCount: number }>>('/admin/extract-airports');
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '提取机场信息失败');
  },

  // 获取节点池配置
  getNodePoolConfig: async (): Promise<any> => {
    const response = await api.get<ApiResponse<any>>('/admin/nodepool/config');
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error(response.data.message || '获取节点池配置失败');
  },

  // 更新节点池配置
  updateNodePoolConfig: async (config: any): Promise<void> => {
    const response = await api.put<ApiResponse<null>>('/admin/nodepool/config', config);
    if (!response.data.success) {
      throw new Error(response.data.message || '更新节点池配置失败');
    }
  },

  // 获取节点池订阅内容
  getNodePoolSubscription: async (format?: string, token?: string): Promise<string> => {
    const params = new URLSearchParams();
    if (format) params.append('format', format);
    if (token) params.append('token', token);

    const response = await api.get<string>(`/nodepool/sub?${params}`, {
      responseType: 'text' as any
    });
    return response.data;
  },
};
