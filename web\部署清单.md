# 📋 部署文件清单

## 🚀 完整部署包说明

本部署包包含了机场订阅管理系统的所有生产文件，可以直接上传到服务器运行。

## 📁 文件结构

```
web/                              # 部署根目录
├── start.js                      # Node.js启动脚本 (跨平台)
├── start.bat                     # Windows启动脚本  
├── package.json                  # 主项目配置文件
├── README.md                     # 部署说明文档
├── 部署清单.md                   # 本文件
└── backend/                      # 后端服务
    ├── package.json              # 后端依赖配置
    └── dist/                     # 编译后的文件
        ├── src/                  # 后端服务代码 (183个文件)
        │   ├── index.js          # 主服务入口
        │   ├── database.js       # 数据库配置
        │   ├── config/           # 配置文件
        │   ├── services/         # 服务层
        │   ├── nodepool/         # 节点池功能
        │   └── utils/            # 工具函数
        ├── public/               # 前端静态文件
        │   ├── index.html        # 主页面 (468B)
        │   └── assets/           # 静态资源
        │       ├── index-ClnBWDKN.js    # JS文件 (666KB)
        │       └── index-vdT4KJ9t.css   # CSS文件 (26KB)
        └── shared/               # 共享类型定义
```

## 📊 统计信息

- **总文件数**: 188个文件
- **总大小**: 约1.9MB
- **主要组成**:
  - 后端编译文件: ~1.2MB  
  - 前端静态文件: ~700KB
  - 配置和启动脚本: <10KB

## 🔧 包含功能

### 完整的Web应用
- ✅ 用户前端界面 (React + TypeScript)
- ✅ 管理后台界面 (React + TypeScript)  
- ✅ 后端API服务 (Node.js + Express + TypeScript)
- ✅ 数据库支持 (SQLite)

### 智能功能
- ✅ 订阅链接解析
- ✅ 流量监控统计
- ✅ 节点池管理
- ✅ 自动化任务

### 部署支持
- ✅ 跨平台启动脚本
- ✅ 自动依赖安装
- ✅ 环境检查
- ✅ 错误处理

## 🚀 快速部署

### 方式一：直接运行
1. 解压部署包到服务器
2. 双击 `start.bat` (Windows) 或运行 `node start.js`
3. 访问 http://localhost:3001

### 方式二：命令行部署
```bash
# 1. 进入目录
cd web

# 2. 安装依赖
npm run install-deps

# 3. 启动服务
npm start
```

## 🌐 访问端点

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端界面 | http://localhost:3001 | 用户查看订阅信息 |
| 管理后台 | http://localhost:3001/admin | 管理员操作界面 |
| API接口 | http://localhost:3001/api | REST API服务 |

## ⚙️ 环境要求

- **Node.js**: 16.0.0+ (必需)
- **内存**: 256MB+ (推荐512MB+)
- **存储**: 100MB+ 可用空间
- **网络**: 用于获取订阅信息

## 🛡️ 安全配置

1. **默认管理员密码**: `admin123` (请立即修改)
2. **数据库**: 自动创建在backend目录
3. **日志**: 控制台输出
4. **HTTPS**: 需要单独配置反向代理

## 📞 部署支持

如遇问题，请检查：
- [ ] Node.js版本是否正确 (`node --version`)
- [ ] 端口3001是否可用
- [ ] 文件权限是否正确
- [ ] 网络连接是否正常

---
**部署包创建时间**: $(Get-Date)
**版本**: v1.0.0 