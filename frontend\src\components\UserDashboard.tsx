import { useState, useEffect } from 'react';
import { subscriptionApi } from '../api';
import { Subscription } from '../types';
import { ContentLayout } from './Layout';

export default function UserDashboard() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadSubscriptions();
  }, []);

  const loadSubscriptions = async () => {
    try {
      setLoading(true);
      const data = await subscriptionApi.getSubscriptions();
      setSubscriptions(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载失败');
    } finally {
      setLoading(false);
    }
  };

  const formatTraffic = (gb: number) => {
    if (gb >= 1024) {
      return `${(gb / 1024).toFixed(1)} TB`;
    }
    return `${gb.toFixed(1)} GB`;
  };

  // 复制订阅链接到剪贴板
  const copySubscriptionUrl = async (url: string, name: string) => {
    try {
      await navigator.clipboard.writeText(url);
      alert(`已复制 ${name} 的订阅链接`);
    } catch (err) {
      // 如果 Clipboard API 不可用，使用传统方法
      const textArea = document.createElement('textarea');
      textArea.value = url;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        alert(`已复制 ${name} 的订阅链接`);
      } catch (fallbackErr) {
        alert('复制失败，请手动复制订阅链接');
      }
      document.body.removeChild(textArea);
    }
  };

  // 智能状态判断函数 - 简化版本
  const getSimplifiedStatus = (subscription: any) => {
    const now = new Date();
    const expiryDate = subscription.expiryDate ? new Date(subscription.expiryDate) : null;
    const usedTraffic = subscription.usedTraffic || 0;
    const totalTraffic = subscription.totalTraffic || 0;

    // 检查是否过期
    if (expiryDate && expiryDate < now) {
      return {
        status: 'abnormal',
        text: '异常',
        color: 'text-red-600 bg-red-100'
      };
    }

    // 检查流量是否耗尽
    if (totalTraffic > 0 && usedTraffic >= totalTraffic) {
      return {
        status: 'abnormal',
        text: '异常',
        color: 'text-red-600 bg-red-100'
      };
    }

    // 检查是否无法获取订阅信息
    if (subscription.status === 'suspended' || subscription.status === 'error') {
      return {
        status: 'abnormal',
        text: '异常',
        color: 'text-red-600 bg-red-100'
      };
    }

    // 正常状态
    return {
      status: 'normal',
      text: '正常',
      color: 'text-green-600 bg-green-100'
    };
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={loadSubscriptions}
          className="btn btn-primary"
        >
          重试
        </button>
      </div>
    );
  }

  return (
    <ContentLayout>
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-primary-900 mb-2">机场订阅</h2>
        <p className="text-primary-600">查看机场订阅流量使用情况</p>
      </div>

      {subscriptions.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-primary-500 mb-4">暂无订阅</div>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {subscriptions.map((subscription) => {
            const statusInfo = getSimplifiedStatus(subscription);
            return (
              <div key={subscription.id} className="card relative">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-lg font-semibold text-primary-900 truncate">
                    {subscription.name}
                  </h3>
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color}`}
                  >
                    {statusInfo.text}
                  </span>
                </div>

              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm text-primary-600 mb-1">
                    <span>已用流量</span>
                    <span>
                      {formatTraffic(subscription.usedTraffic || 0)} / {formatTraffic(subscription.totalTraffic)}
                    </span>
                  </div>
                  <div className="w-full bg-primary-200 rounded-full h-2">
                    <div
                      className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${Math.max(0, ((subscription.usedTraffic || 0) / subscription.totalTraffic) * 100)}%`,
                      }}
                    ></div>
                  </div>
                </div>

                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary-900">
                      {formatTraffic(subscription.remainingTraffic)}
                    </div>
                    <div className="text-sm text-primary-600">剩余流量</div>
                  </div>
                </div>

                {/* 复制按钮 - 右下角 */}
                <button
                  onClick={() => copySubscriptionUrl(subscription.url || '', subscription.name)}
                  className="absolute bottom-4 right-4 p-2 text-gray-400 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-colors duration-200 group"
                  title={`复制 ${subscription.name} 的订阅链接`}
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                </button>
              </div>
            );
          })}
        </div>
      )}
    </ContentLayout>
  );
}
