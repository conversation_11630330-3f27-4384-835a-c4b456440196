# Docker环境配置文件
# 复制此文件为 .env 并修改相应的值

# 基本配置
NODE_ENV=development
PORT=3001

# 安全配置 - 请修改为强密码
ADMIN_PASSWORD=admin123456
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long-for-security
SESSION_SECRET=your-session-secret-at-least-32-characters-long-for-security

# CORS配置 - 允许前端3000端口访问
CORS_ORIGIN=http://localhost:3000

# 数据库配置
DATABASE_PATH=./subscriptions.db
DB_POOL_SIZE=10
DB_TIMEOUT=5000

# 日志配置
LOG_LEVEL=info

# 订阅配置
SUBSCRIPTION_TIMEOUT=8000
SUBSCRIPTION_RETRY=3
USER_AGENT=ClashforWindows/0.20.39 