import { Database } from '../database';
import { SubscriptionParser } from '../subscriptionParser';
import { Subscription, CreateSubscriptionRequest, UpdateSubscriptionRequest } from '../../../shared/types';
import { ValidationError, DatabaseError, NetworkError } from '../middleware/errorHandler';
import { validateRequired, validateUrl, validateNumber } from '../middleware/errorHandler';
import logger from '../utils/logger';

export class SubscriptionService {
  private db: Database;
  private parser: SubscriptionParser;

  constructor(db: Database, parser: SubscriptionParser) {
    this.db = db;
    this.parser = parser;
  }

  /**
   * 获取所有订阅
   */
  async getAllSubscriptions(): Promise<Subscription[]> {
    try {
      logger.debug('获取所有订阅');
      const subscriptions = await this.db.getAllSubscriptions();
      logger.info(`成功获取 ${subscriptions.length} 个订阅`);
      return subscriptions;
    } catch (error) {
      logger.error('获取订阅列表失败', error);
      throw new DatabaseError('获取订阅列表失败', error as Error);
    }
  }

  /**
   * 根据ID获取订阅
   */
  async getSubscriptionById(id: string): Promise<Subscription> {
    validateRequired(id, '订阅ID');
    
    try {
      logger.debug('获取订阅详情', { id });
      const subscription = await this.db.getSubscriptionById(id);
      
      if (!subscription) {
        throw new ValidationError(`订阅 ${id} 不存在`);
      }
      
      logger.info(`成功获取订阅: ${subscription.name}`);
      return subscription;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      logger.error('获取订阅详情失败', { id, error });
      throw new DatabaseError('获取订阅详情失败', error as Error);
    }
  }

  /**
   * 创建订阅
   */
  async createSubscription(data: CreateSubscriptionRequest): Promise<Subscription> {
    this.validateSubscriptionData(data);
    
    try {
      logger.debug('创建订阅', { name: data.name, url: this.maskUrl(data.url) });
      
      // 检查URL是否已存在
      const existingSubscriptions = await this.db.getAllSubscriptions();
      const duplicateUrl = existingSubscriptions.find(sub => sub.url === data.url);
      if (duplicateUrl) {
        throw new ValidationError(`订阅链接已存在于订阅 "${duplicateUrl.name}" 中`);
      }

      const subscription = await this.db.createSubscription(data);
      logger.info(`成功创建订阅: ${subscription.name}`);
      return subscription;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      logger.error('创建订阅失败', { data: { ...data, url: this.maskUrl(data.url) }, error });
      throw new DatabaseError('创建订阅失败', error as Error);
    }
  }

  /**
   * 更新订阅
   */
  async updateSubscription(id: string, data: UpdateSubscriptionRequest): Promise<Subscription> {
    validateRequired(id, '订阅ID');
    this.validateUpdateData(data);
    
    try {
      logger.debug('更新订阅', { id, changes: Object.keys(data) });
      
      // 检查订阅是否存在
      await this.getSubscriptionById(id);
      
      // 如果更新URL，检查是否重复
      if (data.url) {
        const existingSubscriptions = await this.db.getAllSubscriptions();
        const duplicateUrl = existingSubscriptions.find(sub => sub.url === data.url && sub.id !== id);
        if (duplicateUrl) {
          throw new ValidationError(`订阅链接已存在于订阅 "${duplicateUrl.name}" 中`);
        }
      }

      const subscription = await this.db.updateSubscription(id, data);
      if (!subscription) {
        throw new ValidationError(`订阅 ${id} 不存在`);
      }
      
      logger.info(`成功更新订阅: ${subscription.name}`);
      return subscription;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      logger.error('更新订阅失败', { id, data, error });
      throw new DatabaseError('更新订阅失败', error as Error);
    }
  }

  /**
   * 删除订阅
   */
  async deleteSubscription(id: string): Promise<void> {
    validateRequired(id, '订阅ID');
    
    try {
      logger.debug('删除订阅', { id });
      
      // 检查订阅是否存在
      const subscription = await this.getSubscriptionById(id);
      
      const success = await this.db.deleteSubscription(id);
      if (!success) {
        throw new DatabaseError('删除操作失败');
      }
      
      logger.info(`成功删除订阅: ${subscription.name}`);
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      logger.error('删除订阅失败', { id, error });
      throw new DatabaseError('删除订阅失败', error as Error);
    }
  }

  /**
   * 解析订阅链接
   */
  async parseSubscription(url: string): Promise<any> {
    validateRequired(url, '订阅链接');
    validateUrl(url, '订阅链接');
    
    if (!SubscriptionParser.isValidSubscriptionUrl(url)) {
      throw new ValidationError('订阅链接格式不正确');
    }

    try {
      logger.subscription('解析开始', url);
      const subscriptionInfo = await this.parser.parseSubscription(url);
      
      const message = subscriptionInfo.status === 'error'
        ? '订阅链接解析失败，但已提取基本信息'
        : '订阅信息解析成功';
      
      logger.subscription('解析完成', url, { 
        status: subscriptionInfo.status,
        name: subscriptionInfo.name 
      });
      
      return { subscriptionInfo, message };
    } catch (error) {
      logger.error('解析订阅失败', { url: this.maskUrl(url), error });
      throw new NetworkError(`解析订阅失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取用户前端订阅列表（简化版）
   */
  async getUserSubscriptions(): Promise<Partial<Subscription>[]> {
    try {
      const subscriptions = await this.getAllSubscriptions();
      
      // 只返回用户需要的基本信息
      return subscriptions.map(sub => ({
        id: sub.id,
        name: sub.name,
        url: sub.url,
        remainingTraffic: sub.remainingTraffic,
        totalTraffic: sub.totalTraffic,
        usedTraffic: sub.usedTraffic,
        expiryDate: sub.expiryDate,
        status: sub.status
      }));
    } catch (error) {
      logger.error('获取用户订阅列表失败', error);
      throw new DatabaseError('获取订阅列表失败', error as Error);
    }
  }

  /**
   * 刷新所有订阅的流量信息
   */
  async refreshAllSubscriptions(): Promise<{ successCount: number; failureCount: number }> {
    try {
      logger.info('开始刷新所有订阅');
      const result = await this.db.recordTrafficWithRefresh(this.parser);
      logger.info(`刷新完成: 成功 ${result.successCount} 个，失败 ${result.failureCount} 个`);
      return result;
    } catch (error) {
      logger.error('刷新订阅失败', error);
      throw new DatabaseError('刷新订阅失败', error as Error);
    }
  }

  /**
   * 验证订阅数据
   */
  private validateSubscriptionData(data: CreateSubscriptionRequest): void {
    validateRequired(data.name, '订阅名称');
    validateRequired(data.url, '订阅链接');
    validateRequired(data.expiryDate, '到期时间');
    validateNumber(data.totalTraffic, '总流量', 0);
    
    validateUrl(data.url, '订阅链接');
    
    // 验证到期时间格式
    try {
      const expiryDate = new Date(data.expiryDate);
      if (isNaN(expiryDate.getTime())) {
        throw new ValidationError('到期时间格式不正确');
      }
    } catch {
      throw new ValidationError('到期时间格式不正确');
    }
    
    if (data.usedTraffic !== undefined) {
      validateNumber(data.usedTraffic, '已用流量', 0);
    }
  }

  /**
   * 验证更新数据
   */
  private validateUpdateData(data: UpdateSubscriptionRequest): void {
    if (Object.keys(data).length === 0) {
      throw new ValidationError('更新数据不能为空');
    }
    
    if (data.name !== undefined) {
      validateRequired(data.name, '订阅名称');
    }
    
    if (data.url !== undefined) {
      validateRequired(data.url, '订阅链接');
      validateUrl(data.url, '订阅链接');
    }
    
    if (data.totalTraffic !== undefined) {
      validateNumber(data.totalTraffic, '总流量', 0);
    }
    
    if (data.usedTraffic !== undefined) {
      validateNumber(data.usedTraffic, '已用流量', 0);
    }
    
    if (data.expiryDate !== undefined) {
      try {
        const expiryDate = new Date(data.expiryDate);
        if (isNaN(expiryDate.getTime())) {
          throw new ValidationError('到期时间格式不正确');
        }
      } catch {
        throw new ValidationError('到期时间格式不正确');
      }
    }
  }

  /**
   * 隐藏URL中的敏感信息
   */
  private maskUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      if (urlObj.searchParams.has('token')) {
        urlObj.searchParams.set('token', '***');
      }
      return urlObj.toString();
    } catch {
      return url.replace(/token=[^&]+/g, 'token=***');
    }
  }
} 