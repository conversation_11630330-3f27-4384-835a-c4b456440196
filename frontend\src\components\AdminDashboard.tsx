import { Routes, Route } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import Login from './Login';
import AdminSidebar from './AdminSidebar';
import AdminOverview from './AdminOverview';
import AdminSubscriptions from './AdminSubscriptions';
import AdminAnalytics from './AdminAnalytics';
import AdminNodePool from './AdminNodePool';
import AdminAirports from './AdminAirports';
import AdminSettings from './AdminSettings';

export default function AdminDashboard() {
  const { isAuthenticated, login } = useAuth();

  // 如果未认证，显示登录页面
  if (!isAuthenticated) {
    return <Login onLogin={login} />;
  }

  return <AuthenticatedAdminDashboard />;
}

function AuthenticatedAdminDashboard() {
  return (
    <div className="h-screen bg-gray-100 overflow-hidden">
      <div className="flex h-full">
        {/* 侧栏 */}
        <AdminSidebar />

        {/* 主内容区域 */}
        <div className="flex-1 h-full overflow-hidden">
          {/* 主内容 */}
          <main className="h-full overflow-y-auto bg-gray-100 relative">
            {/* 内容区域 */}
            <div className="p-6 min-h-full">
              <div className="max-w-7xl mx-auto">
                <Routes>
                  <Route path="/" element={<AdminOverview />} />
                  <Route path="/subscriptions/*" element={<AdminSubscriptions />} />
                  <Route path="/analytics" element={<AdminAnalytics />} />
                  <Route path="/nodepool" element={<AdminNodePool />} />
                  <Route path="/airports" element={<AdminAirports />} />
                  <Route path="/settings" element={<AdminSettings />} />
                </Routes>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}
