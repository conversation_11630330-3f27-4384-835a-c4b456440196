interface LogLevel {
  value: number;
  label: string;
  color?: string;
}

const LOG_LEVELS: Record<string, LogLevel> = {
  ERROR: { value: 0, label: 'ERROR', color: '\x1b[31m' }, // 红色
  WARN: { value: 1, label: 'WARN', color: '\x1b[33m' },   // 黄色
  INFO: { value: 2, label: 'INFO', color: '\x1b[36m' },   // 青色
  DEBUG: { value: 3, label: 'DEBUG', color: '\x1b[35m' }, // 紫色
};

const RESET_COLOR = '\x1b[0m';

class Logger {
  private currentLevel: number;
  private enableColors: boolean;

  constructor(level: string = 'INFO', enableColors: boolean = true) {
    this.currentLevel = LOG_LEVELS[level.toUpperCase()]?.value ?? LOG_LEVELS.INFO.value;
    this.enableColors = enableColors && process.stdout.isTTY;
  }

  private formatMessage(level: LogLevel, message: string, meta?: any): string {
    const timestamp = new Date().toISOString();
    const coloredLevel = this.enableColors 
      ? `${level.color}[${level.label}]${RESET_COLOR}`
      : `[${level.label}]`;
    
    let formattedMessage = `${timestamp} ${coloredLevel} ${message}`;
    
    if (meta) {
      formattedMessage += `\n${this.formatMeta(meta)}`;
    }
    
    return formattedMessage;
  }

  private formatMeta(meta: any): string {
    if (meta instanceof Error) {
      return `Error: ${meta.message}\nStack: ${meta.stack}`;
    }
    
    if (typeof meta === 'object') {
      try {
        return JSON.stringify(meta, null, 2);
      } catch {
        return String(meta);
      }
    }
    
    return String(meta);
  }

  private log(level: LogLevel, message: string, meta?: any): void {
    if (level.value <= this.currentLevel) {
      const formattedMessage = this.formatMessage(level, message, meta);
      
      if (level.value === 0) { // ERROR
        console.error(formattedMessage);
      } else if (level.value === 1) { // WARN
        console.warn(formattedMessage);
      } else {
        console.log(formattedMessage);
      }
    }
  }

  error(message: string, meta?: any): void {
    this.log(LOG_LEVELS.ERROR, message, meta);
  }

  warn(message: string, meta?: any): void {
    this.log(LOG_LEVELS.WARN, message, meta);
  }

  info(message: string, meta?: any): void {
    this.log(LOG_LEVELS.INFO, message, meta);
  }

  debug(message: string, meta?: any): void {
    this.log(LOG_LEVELS.DEBUG, message, meta);
  }

  // 便捷方法
  request(method: string, path: string, statusCode: number, duration: number): void {
    const color = statusCode >= 400 ? LOG_LEVELS.ERROR.color : LOG_LEVELS.INFO.color;
    const coloredStatus = this.enableColors 
      ? `${color}${statusCode}${RESET_COLOR}`
      : statusCode;
    
    this.info(`${method} ${path} ${coloredStatus} - ${duration}ms`);
  }

  database(operation: string, table: string, duration?: number): void {
    const meta = duration ? { operation, table, duration: `${duration}ms` } : { operation, table };
    this.debug('数据库操作', meta);
  }

  subscription(action: string, url: string, result?: any): void {
    this.info(`订阅${action}: ${this.maskUrl(url)}`, result);
  }

  nodePool(action: string, meta?: any): void {
    this.info(`节点池${action}`, meta);
  }

  private maskUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      // 隐藏敏感参数
      if (urlObj.searchParams.has('token')) {
        urlObj.searchParams.set('token', '***');
      }
      return urlObj.toString();
    } catch {
      return url.replace(/token=[^&]+/g, 'token=***');
    }
  }

  // 设置日志级别
  setLevel(level: string): void {
    const newLevel = LOG_LEVELS[level.toUpperCase()];
    if (newLevel) {
      this.currentLevel = newLevel.value;
      this.info(`日志级别已设置为: ${level.toUpperCase()}`);
    } else {
      this.warn(`无效的日志级别: ${level}，保持当前级别`);
    }
  }

  // 获取当前级别
  getLevel(): string {
    const level = Object.values(LOG_LEVELS).find(l => l.value === this.currentLevel);
    return level?.label || 'UNKNOWN';
  }
}

// 创建默认实例
const logger = new Logger(
  process.env.LOG_LEVEL || 'INFO',
  process.env.NODE_ENV !== 'production'
);

export default logger;
export { Logger, LOG_LEVELS }; 