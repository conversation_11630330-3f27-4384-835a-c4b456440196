import { useState, useEffect } from 'react';
import { adminApi } from '../api';

interface NodePoolConfig {
  nodeLinks: string;
  subscriptionLinks: string;
  subscriptionName: string; // 订阅名称
  totalTraffic: number; // GB
  expiryDate: string;
  updateInterval: number; // 小时
  subConverter: string; // 订阅转换后端
  subConfig: string; // 订阅配置文件
  token: string; // 访问令牌
  guestToken: string; // 访客令牌
  subscriptionDays: number; // 订阅周期天数
}

interface NodePoolStats {
  totalNodes: number;
  validNodes: number;
  sourceCount: number;
  lastUpdate: string;
  trafficInfo: {
    upload: number;
    download: number;
    total: number;
    expire: number;
    used: number;
    remaining: number;
    usagePercent: number;
    timePercent: number;
  };
}

// interface CacheStatus {
//   isValid: boolean;
//   lastUpdate: string;
//   nextUpdate: string;
//   nodeCount: number;
// }

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export default function AdminNodePool() {
  const [config, setConfig] = useState<NodePoolConfig>({
    nodeLinks: '',
    subscriptionLinks: '',
    subscriptionName: '',
    totalTraffic: 99,
    expiryDate: '2099-12-31',
    updateInterval: 6,
    subConverter: 'SUBAPI.cmliussss.net',
    subConfig: 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/Clash/config/ACL4SSR_Online_MultiCountry.ini',
    token: 'auto',
    guestToken: '',
    subscriptionDays: 30
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [validating, setValidating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // 新增状态
  const [stats, setStats] = useState<NodePoolStats | null>(null);
  // const [cacheStatus, setCacheStatus] = useState<CacheStatus | null>(null);
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  // const [debugInfo, setDebugInfo] = useState<any>(null);
  
  // 订阅链接生成
  const [generatedLinks, setGeneratedLinks] = useState<{[key: string]: string}>({});
  // const [showQRCode, setShowQRCode] = useState<{[key: string]: boolean}>({});

  useEffect(() => {
    loadConfig();
  }, []);

  useEffect(() => {
    // 只有在config完全加载后才生成订阅链接
    if (config && config.token) {
      generateSubscriptionLinks();
    }
  }, [config?.token]);

  const loadConfig = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('开始加载配置...');
      
      const newConfig = await adminApi.getNodePoolConfig();
      console.log('API响应数据:', newConfig);
      console.log('API响应数据类型:', typeof newConfig);
      
      console.log('新配置对象:', newConfig);
      console.log('新配置对象类型:', typeof newConfig);
      console.log('新配置对象是否为null:', newConfig === null);
      console.log('新配置对象是否为undefined:', newConfig === undefined);
      console.log('新配置对象keys:', newConfig ? Object.keys(newConfig) : 'no keys');
      
      // 添加安全检查
      if (!newConfig) {
        console.error('接收到的配置为空');
        setError('接收到的配置数据为空');
        return;
      }
      
      console.log('nodeLinks存在:', !!newConfig.nodeLinks);
      console.log('nodeLinks长度:', newConfig.nodeLinks ? newConfig.nodeLinks.length : 0);
      console.log('nodeLinks类型:', typeof newConfig.nodeLinks);
      
      if (newConfig.nodeLinks) {
        const lines = newConfig.nodeLinks.split('\n').filter((line: string) => line.trim());
        console.log('nodeLinks有效行数:', lines.length);
        console.log('nodeLinks前100字符:', newConfig.nodeLinks.substring(0, 100));
      }
      
      setConfig(newConfig);
      console.log('配置已设置到状态');
      
      // 直接使用新配置加载统计信息，而不是依赖状态
      await loadStatsWithConfig(newConfig);
      
      // 立即生成订阅链接
      if (newConfig && newConfig.token) {
        generateSubscriptionLinksWithConfig(newConfig);
      }
    } catch (err) {
      console.error('加载配置失败，错误详情:', err);
      console.error('错误类型:', typeof err);
      console.error('错误消息:', err instanceof Error ? err.message : String(err));
      setError(err instanceof Error ? err.message : '加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    await loadStatsWithConfig(config);
  };

  const loadStatsWithConfig = async (configData: NodePoolConfig | null) => {
    try {
      // 确保config已经加载
      if (!configData || !configData.token) {
        console.log('配置未加载，跳过统计信息加载');
        return;
      }
      
      const token = configData.token || 'auto';
      const debugResponse = await fetch(`/api/nodepool/debug?token=${token}`);
      if (debugResponse.ok) {
        const debugData = await debugResponse.json();
        if (debugData.success) {
          setStats(debugData.data.stats);
        }
      } else {
        console.error('获取调试信息失败:', debugResponse.status, debugResponse.statusText);
      }
    } catch (err) {
      console.error('加载统计信息失败:', err);
    }
  };

  const generateSubscriptionLinksWithConfig = (configData: NodePoolConfig) => {
    console.log('生成订阅链接，配置:', configData);
    const baseUrl = window.location.origin;
    const token = configData.token || 'auto';

    const links = {
      auto: `${baseUrl}/api/nodepool/sub?token=${token}`,
      base64: `${baseUrl}/api/nodepool/sub?token=${token}&format=base64`,
      clash: `${baseUrl}/api/nodepool/sub?token=${token}&format=clash`,
      v2ray: `${baseUrl}/api/nodepool/sub?token=${token}&format=v2ray`,
      raw: `${baseUrl}/api/nodepool/raw?token=${token}`
    };

    console.log('生成的订阅链接:', links);
    setGeneratedLinks(links);
  };

  const generateSubscriptionLinks = () => {
    // 添加安全检查
    if (!config || !config.token) {
      console.log('配置未加载，跳过订阅链接生成，config:', config);
      return;
    }
    
    generateSubscriptionLinksWithConfig(config);
  };

  const saveConfig = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);
      
      // 添加安全检查
      if (!config) {
        setError('配置未加载，无法保存');
        return;
      }
      
      await adminApi.updateNodePoolConfig(config);
      setSuccess('节点池配置保存成功');
      
      // 重新生成订阅链接
      generateSubscriptionLinks();
      
      // 重新加载统计信息
      await loadStats();
      
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存配置失败');
    } finally {
      setSaving(false);
    }
  };

  const testConfiguration = async () => {
    try {
      setTesting(true);
      setError(null);
      
      // 添加安全检查
      if (!config || !config.token) {
        setError('配置未加载，无法测试');
        return;
      }
      
      const token = config.token || 'auto';
      const response = await fetch(`/api/nodepool/validate?token=${token}`);
      const data = await response.json();
      
      if (data.success) {
        setValidation(data.data.validation);
        if (data.data.validation.isValid) {
          setSuccess('配置测试通过！');
        } else {
          setError(`配置测试失败：${data.data.validation.errors.join(', ')}`);
        }
      } else {
        setError(data.message || '测试失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '测试失败');
    } finally {
      setTesting(false);
    }
  };

  const refreshNodes = async () => {
    try {
      setValidating(true);
      setError(null);
      
      // 添加安全检查
      if (!config) {
        setError('配置未加载，无法刷新');
        return;
      }
      
      // 先保存配置
      await adminApi.updateNodePoolConfig(config);
      
      // 然后重新加载统计信息
      await loadStats();
      
      setSuccess('节点已刷新');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : '刷新节点失败');
    } finally {
      setValidating(false);
    }
  };

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setSuccess(`${type}订阅链接已复制到剪贴板`);
      setTimeout(() => setSuccess(null), 2000);
    } catch (err) {
      setError('复制失败，请手动复制');
      setTimeout(() => setError(null), 2000);
    }
  };

  const openTestPage = () => {
    window.open('/nodepool/test', '_blank');
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">节点池管理</h1>
          <p className="text-gray-500 mt-1">智能节点聚合和订阅转换 - 新架构</p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-gray-900 border-t-transparent"></div>
          <span className="ml-3 text-gray-600">加载中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">节点池管理</h1>
          <p className="text-gray-500 mt-1">智能节点聚合和订阅转换 - 新架构</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={openTestPage}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            测试页面
          </button>
          <button
            onClick={testConfiguration}
            disabled={testing}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {testing ? '测试中...' : '测试配置'}
          </button>
          <button
            onClick={refreshNodes}
            disabled={validating}
            className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            {validating ? '刷新中...' : '刷新节点'}
          </button>
        </div>
      </div>

      {/* 错误和成功消息 */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
          {success}
        </div>
      )}

      {/* 验证结果 */}
      {validation && (
        <div className={`border rounded-md p-4 ${validation.isValid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
          <h3 className={`font-medium ${validation.isValid ? 'text-green-800' : 'text-red-800'}`}>
            配置验证结果
          </h3>
          {validation.errors.length > 0 && (
            <div className="mt-2">
              <p className="text-red-700 font-medium">错误：</p>
              <ul className="list-disc list-inside text-red-600">
                {validation.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
          {validation.warnings.length > 0 && (
            <div className="mt-2">
              <p className="text-yellow-700 font-medium">警告：</p>
              <ul className="list-disc list-inside text-yellow-600">
                {validation.warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* 统计信息面板 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总节点数</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalNodes}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">有效节点</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.validNodes}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">节点源</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.sourceCount}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow border">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">最后更新</p>
                <p className="text-sm font-semibold text-gray-900">{formatDate(stats.lastUpdate)}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 流量信息面板 */}
      {stats?.trafficInfo && (
        <div className="bg-white rounded-lg shadow border">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">流量信息</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <p className="text-sm font-medium text-gray-500">已用流量</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatBytes(stats.trafficInfo.used)}
                </p>
                <p className="text-sm text-gray-500">
                  {stats.trafficInfo.usagePercent.toFixed(1)}%
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">剩余流量</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatBytes(stats.trafficInfo.remaining)}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">总流量</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatBytes(stats.trafficInfo.total)}
                </p>
              </div>
            </div>
            
            {/* 进度条 */}
            <div className="mt-6">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>使用进度</span>
                <span>{stats.trafficInfo.usagePercent.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${
                    stats.trafficInfo.usagePercent < 70 ? 'bg-green-600' : 
                    stats.trafficInfo.usagePercent < 90 ? 'bg-yellow-600' : 'bg-red-600'
                  }`}
                  style={{ width: `${Math.min(stats.trafficInfo.usagePercent, 100)}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 节点池配置 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-2">节点池配置</h2>
          <p className="text-sm text-gray-500">配置节点链接和订阅源</p>
        </div>

        <div className="space-y-6">
          {/* 节点链接 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              节点链接
            </label>
            <textarea
              value={config?.nodeLinks || ''}
              onChange={(e) => setConfig(prev => ({ ...(prev || {}), nodeLinks: e.target.value }))}
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="每行一个节点链接，支持 vmess://、vless://、trojan://、ss:// 等格式"
            />
          </div>

          {/* 订阅链接 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              订阅链接
            </label>
            <textarea
              value={config?.subscriptionLinks || ''}
              onChange={(e) => setConfig(prev => ({ ...(prev || {}), subscriptionLinks: e.target.value }))}
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="每行一个订阅链接，支持各种机场订阅"
            />
          </div>

          {/* 流量和时间设置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                总流量 (GB)
              </label>
              <input
                type="number"
                min="1"
                max="10000"
                value={config?.totalTraffic || 99}
                onChange={(e) => setConfig(prev => ({ ...(prev || {}), totalTraffic: parseInt(e.target.value) || 99 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                过期日期
              </label>
              <input
                type="date"
                value={config?.expiryDate || '2099-12-31'}
                onChange={(e) => setConfig(prev => ({ ...(prev || {}), expiryDate: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                订阅周期 (天)
              </label>
              <input
                type="number"
                min="1"
                max="365"
                value={config?.subscriptionDays || 30}
                onChange={(e) => setConfig(prev => ({ ...(prev || {}), subscriptionDays: parseInt(e.target.value) || 30 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                更新间隔 (小时)
              </label>
              <input
                type="number"
                min="1"
                max="168"
                value={config?.updateInterval || 6}
                onChange={(e) => setConfig(prev => ({ ...(prev || {}), updateInterval: parseInt(e.target.value) || 6 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* 访问控制设置 */}
          <div className="space-y-4">
            <h3 className="text-md font-medium text-gray-900">访问控制</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  访问令牌
                </label>
                <input
                  type="text"
                  value={config?.token || ''}
                  onChange={(e) => setConfig(prev => ({ ...(prev || {}), token: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="例如：auto"
                />
                <p className="text-xs text-gray-400 mt-1">
                  用于订阅链接的访问验证
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  访客令牌
                </label>
                <input
                  type="text"
                  value={config?.guestToken || ''}
                  onChange={(e) => setConfig(prev => ({ ...(prev || {}), guestToken: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="可选，留空自动生成"
                />
                <p className="text-xs text-gray-400 mt-1">
                  访客订阅的专用令牌
                </p>
              </div>
            </div>
          </div>

          {/* 订阅名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              订阅名称
            </label>
            <input
              type="text"
              value={config?.subscriptionName || ''}
              onChange={(e) => setConfig(prev => ({ ...(prev || {}), subscriptionName: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="例如：中国节点池"
            />
          </div>

          {/* 生成的订阅链接 */}
          {Object.keys(generatedLinks).length > 0 && (
            <div className="space-y-4">
              <h3 className="text-md font-medium text-gray-900">生成的订阅链接</h3>
              <div className="grid grid-cols-1 gap-4">
                {Object.entries(generatedLinks).map(([format, link]) => {
                  const formatNames: {[key: string]: string} = {
                    auto: '智能订阅',
                    base64: 'Base64订阅',
                    clash: 'Clash订阅',
                    v2ray: 'V2Ray订阅',
                    raw: '原始节点'
                  };
                  
                  return (
                    <div key={format} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-sm font-medium text-gray-700">{formatNames[format] || format}</h4>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => copyToClipboard(link, formatNames[format] || format)}
                            className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                          >
                            复制链接
                          </button>
                          <button
                            onClick={() => window.open(link, '_blank')}
                            className="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                          >
                            测试链接
                          </button>
                        </div>
                      </div>
                      <div className="bg-white rounded border p-2">
                        <code className="text-xs text-gray-600 break-all">{link}</code>
                      </div>
                    </div>
                  );
                })}
              </div>
              
              {/* 使用说明 */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-800 mb-2">📖 使用说明</h4>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li><strong>智能订阅:</strong> 自动检测客户端类型，返回最合适的格式</li>
                  <li><strong>Base64订阅:</strong> 标准Base64编码，兼容大部分V2Ray客户端</li>
                  <li><strong>Clash订阅:</strong> Clash/ClashX专用格式，支持规则分流</li>
                  <li><strong>V2Ray订阅:</strong> 原生V2Ray格式，适合高级用户</li>
                  <li><strong>原始节点:</strong> 纯文本节点列表，方便调试和检查</li>
                </ul>
              </div>
            </div>
          )}

          <div className="pt-4">
            <button
              onClick={saveConfig}
              disabled={saving}
              className="px-6 py-2 bg-gray-900 text-white text-sm rounded-lg hover:bg-gray-800 focus:outline-none transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  <span>保存中...</span>
                </>
              ) : (
                <>
                  <span>💾</span>
                  <span>保存配置</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
