import { NodeCollector, NodeSource, CollectedNode } from './NodeCollector';
import { SubscriptionGenerator, SubscriptionConfig, GeneratedSubscription } from './SubscriptionGenerator';
import { TrafficManager, TrafficConfig, TrafficInfo } from './TrafficManager';

export interface NodePoolConfig {
  nodeLinks: string;
  subscriptionLinks: string;
  totalTraffic: number;
  expiryDate: string;
  updateInterval: number;
  subConverter?: string;
  subConfig?: string;
  token?: string;
  guestToken?: string;
  subscriptionDays?: number;
  botToken?: string;
  chatId?: string;
}

export interface NodePoolStats {
  totalNodes: number;
  validNodes: number;
  sourceCount: number;
  lastUpdate: Date;
  trafficInfo: TrafficInfo;
}

/**
 * 节点池管理器 - 新架构的核心类
 * 
 * 整合节点收集、订阅生成和流量管理功能
 * 基于Express + SQLite架构设计，解决CloudFlare Workers移植问题
 */
export class NodePoolManager {
  private nodeCollector: NodeCollector;
  private subscriptionGenerator: SubscriptionGenerator;
  private trafficManager: TrafficManager;
  private cachedNodes: CollectedNode[] = [];
  private lastUpdateTime: Date = new Date(0);

  constructor(private config: NodePoolConfig) {
    this.nodeCollector = new NodeCollector();
    this.subscriptionGenerator = new SubscriptionGenerator({
      subConverter: config.subConverter,
      subConfig: config.subConfig
    });
    this.trafficManager = new TrafficManager();
    
    console.log('NodePoolManager 初始化完成');
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: NodePoolConfig): void {
    this.config = newConfig;
    this.subscriptionGenerator = new SubscriptionGenerator({
      subConverter: newConfig.subConverter,
      subConfig: newConfig.subConfig
    });
    console.log('节点池配置已更新');
  }

  /**
   * 获取所有节点（带缓存）
   */
  async getAllNodes(forceRefresh = false): Promise<CollectedNode[]> {
    const shouldUpdate = forceRefresh || this.shouldUpdateCache();
    
    if (shouldUpdate) {
      console.log('开始更新节点缓存...');
      await this.refreshNodes();
    } else {
      console.log('使用缓存的节点数据');
    }
    
    return this.cachedNodes;
  }

  /**
   * 强制刷新节点
   */
  async refreshNodes(): Promise<void> {
    try {
      const sources = this.parseNodeSources();
      this.cachedNodes = await this.nodeCollector.collectNodes(sources);
      this.lastUpdateTime = new Date();
      
      console.log(`节点刷新完成，获取到 ${this.cachedNodes.length} 个节点`);
    } catch (error) {
      console.error('刷新节点失败:', error);
      throw error;
    }
  }

  /**
   * 生成订阅内容
   */
  async generateSubscription(format: string): Promise<GeneratedSubscription> {
    const nodes = await this.getAllNodes();
    
    switch (format.toLowerCase()) {
      case 'base64':
        return this.subscriptionGenerator.generateBase64(nodes);
      case 'clash':
        return this.subscriptionGenerator.generateClash(nodes);
      case 'v2ray':
      case 'raw':
        return this.subscriptionGenerator.generateV2Ray(nodes);
      default:
        throw new Error(`不支持的订阅格式: ${format}`);
    }
  }

  /**
   * 获取流量信息
   */
  getTrafficInfo(debug: boolean = false): TrafficInfo {
    const trafficConfig: TrafficConfig = {
      totalTraffic: this.config.totalTraffic,
      expiryDate: this.config.expiryDate,
      subscriptionDays: this.config.subscriptionDays
    };
    
    return this.trafficManager.calculateTrafficInfo(trafficConfig, debug);
  }

  /**
   * 检查订阅是否过期
   */
  isExpired(): boolean {
    const trafficConfig: TrafficConfig = {
      totalTraffic: this.config.totalTraffic,
      expiryDate: this.config.expiryDate,
      subscriptionDays: this.config.subscriptionDays
    };
    
    return this.trafficManager.isExpired(trafficConfig);
  }

  /**
   * 获取节点池统计信息
   */
  async getStats(debug: boolean = false): Promise<NodePoolStats> {
    const nodes = await this.getAllNodes();
    const validNodes = nodes.filter(node => node.isValid);
    const sources = this.parseNodeSources();
    const trafficInfo = this.getTrafficInfo(debug);
    
    return {
      totalNodes: nodes.length,
      validNodes: validNodes.length,
      sourceCount: sources.length,
      lastUpdate: this.lastUpdateTime,
      trafficInfo
    };
  }

  /**
   * 生成调试信息
   */
  async generateDebugInfo(): Promise<{
    config: NodePoolConfig;
    stats: NodePoolStats;
    sources: NodeSource[];
    sampleNodes: CollectedNode[];
    trafficReport: string;
  }> {
    const nodes = await this.getAllNodes();
    // 在调试模式下启用详细的流量计算日志
    const stats = await this.getStats(true);
    const sources = this.parseNodeSources();
    const trafficConfig: TrafficConfig = {
      totalTraffic: this.config.totalTraffic,
      expiryDate: this.config.expiryDate,
      subscriptionDays: this.config.subscriptionDays
    };
    const trafficReport = this.trafficManager.generateTrafficReport(trafficConfig);
    
    return {
      config: this.config,
      stats,
      sources,
      sampleNodes: nodes.slice(0, 5), // 只返回前5个节点作为示例
      trafficReport
    };
  }

  /**
   * 验证节点池配置
   */
  async validateConfig(): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // 验证基本配置
    if (!this.config.nodeLinks && !this.config.subscriptionLinks) {
      errors.push('必须配置节点链接或订阅链接');
    }
    
    if (this.config.totalTraffic <= 0) {
      errors.push('总流量必须大于0');
    }
    
    const expiryDate = new Date(this.config.expiryDate);
    if (isNaN(expiryDate.getTime())) {
      errors.push('过期时间格式无效');
    } else if (expiryDate.getTime() < Date.now()) {
      warnings.push('订阅已过期');
    }
    
    // 测试节点获取
    try {
      const nodes = await this.getAllNodes();
      if (nodes.length === 0) {
        warnings.push('没有获取到任何节点');
      } else {
        const validNodes = nodes.filter(node => node.isValid);
        if (validNodes.length === 0) {
          errors.push('没有有效的节点');
        } else if (validNodes.length < nodes.length) {
          warnings.push(`${nodes.length - validNodes.length} 个节点无效`);
        }
      }
    } catch (error) {
      errors.push(`节点获取失败: ${error}`);
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 解析节点源配置
   */
  private parseNodeSources(): NodeSource[] {
    const sources: NodeSource[] = [];
    
    if (this.config.nodeLinks && this.config.nodeLinks.trim()) {
      sources.push({
        type: 'direct',
        content: this.config.nodeLinks,
        enabled: true
      });
    }
    
    if (this.config.subscriptionLinks && this.config.subscriptionLinks.trim()) {
      sources.push({
        type: 'subscription',
        content: this.config.subscriptionLinks,
        enabled: true
      });
    }
    
    return sources;
  }

  /**
   * 判断是否需要更新缓存
   */
  private shouldUpdateCache(): boolean {
    const now = new Date();
    const timeDiff = now.getTime() - this.lastUpdateTime.getTime();
    const updateIntervalMs = this.config.updateInterval * 60 * 60 * 1000; // 小时转毫秒
    
    return timeDiff >= updateIntervalMs || this.cachedNodes.length === 0;
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus(): {
    isValid: boolean;
    lastUpdate: Date;
    nextUpdate: Date;
    nodeCount: number;
  } {
    const now = new Date();
    const updateIntervalMs = this.config.updateInterval * 60 * 60 * 1000;
    const nextUpdate = new Date(this.lastUpdateTime.getTime() + updateIntervalMs);
    
    return {
      isValid: !this.shouldUpdateCache(),
      lastUpdate: this.lastUpdateTime,
      nextUpdate,
      nodeCount: this.cachedNodes.length
    };
  }
} 